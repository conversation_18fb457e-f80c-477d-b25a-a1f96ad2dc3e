"""
Refactored Master Node Entry Point

This file serves as the clean entry point for the master node, handling only:
- Application initialization and configuration
- Lifespan management (startup/shutdown)
- Router registration
- Exception handler setup

All API endpoints have been moved to separate router modules for better organization.
"""

import asyncio
import gzip
import hashlib
import json
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from api.dependencies.services import set_rule_service
from api.middleware.error_handling import UnifiedErrorHandlingMiddleware
from api.routers.common.validation_logic import unified_validation_worker
from api.routers.master import master_routers
from api.routers.master.validation import set_request_queue
from config.monitoring_config import monitoring_config
from config.settings import settings
from config.validation import validate_rule_registration_config
from core.database_initializer import get_database_info, initialize_database
from core.db_session import get_session_factory, start_db_pool_monitoring, stop_db_pool_monitoring
from core.logging.logging_system import log as logger
from models.database import RuleStatusEnum, RuleTemplate
from services.data_mapping_engine import DataMappingEngine
from services.difference_analyzer import DifferenceAnalyzer
from services.monitoring_service import monitoring_service
from services.rule_change_detector import RuleChangeDetector
from services.rule_loader import load_rules_from_db, load_rules_into_cache
from services.rule_registration_service import RuleRegistrationService
from services.rule_service import RuleService
from services.task_status_manager import TaskStatus, get_task_status_manager
from services.template_generator import TemplateGenerator

# --- Globals for Queue-based Validation ---
REQUEST_QUEUE: asyncio.Queue | None = None
REGISTRATION_QUEUE: asyncio.Queue | None = None
WORKER_TASKS: list[asyncio.Task] = []
REGISTRATION_WORKER_TASKS: list[asyncio.Task] = []
OFFLINE_QUEUE_PROCESSOR_TASK: asyncio.Task | None = None
rule_service: RuleService | None = None

# --- Constants ---
LOCAL_RULES_PATH = "rules_cache.json.gz"
LOCAL_VERSION_PATH = "rules_version.txt"


def generate_rules_cache_file():
    """
    生成规则数据的压缩文件，供子节点使用。

    这个函数会：
    1. 从数据库加载所有活跃的规则数据
    2. 将规则数据序列化为RuleDataSet格式
    3. 生成版本哈希
    4. 压缩数据并保存到本地文件

    Returns:
        bool: 是否成功生成文件
    """
    try:
        logger.info("开始生成规则缓存文件...")

        # 从数据库加载RuleDataSet对象
        session_factory = get_session_factory()
        with session_factory() as session:
            db_rules = load_rules_from_db(session)

        if not db_rules:
            logger.warning("数据库中没有规则数据，跳过缓存文件生成")
            return False

        # 计算版本哈希（基于规则数据）
        rule_keys = sorted([rule.base_rule.rule_key for rule in db_rules if rule.base_rule])
        version_string = "".join(rule_keys)
        version_hash = hashlib.sha256(version_string.encode("utf-8")).hexdigest()

        # 将RuleDataSet对象序列化为字典列表
        exported_rule_datasets = []
        for rule_dataset in db_rules:
            try:
                exported_rule_datasets.append(rule_dataset.to_dict())
            except Exception as e:
                logger.error(f"序列化规则数据集失败 {rule_dataset.id}: {e}")
                continue

        # 创建导出包，格式与子节点期望的格式匹配
        package = {
            "version": version_hash,
            "rule_datasets": exported_rule_datasets,
            "export_timestamp": datetime.now().isoformat(),
            "total_count": len(exported_rule_datasets),
        }

        # 序列化并压缩数据
        json_data = json.dumps(package, ensure_ascii=False, indent=None)
        gzipped_data = gzip.compress(json_data.encode("utf-8"))

        # 安全地写入文件
        temp_path = f"{LOCAL_RULES_PATH}.tmp"
        with open(temp_path, "wb") as f:
            f.write(gzipped_data)
        os.replace(temp_path, LOCAL_RULES_PATH)

        # 更新版本文件
        with open(LOCAL_VERSION_PATH, "w") as f:
            f.write(version_hash)

        logger.info(
            f"规则缓存文件生成成功: {len(exported_rule_datasets)} 个规则数据集, "
            f"{len(gzipped_data)} 字节, 版本: {version_hash[:8]}..."
        )
        return True

    except Exception as e:
        logger.error(f"生成规则缓存文件失败: {e}", exc_info=True)
        return False


# --- 注意：原有的queue_worker函数已被统一的validation_worker替代 ---
# 统一的validation_worker在api.routers.common.validation_logic中定义


# --- Registration Worker for Async Task Processing ---
async def registration_worker(worker_id: int):
    """处理注册队列中的异步任务"""
    logger.info(f"[RegistrationWorker-{worker_id}] starting.")

    # 获取服务实例
    task_manager = get_task_status_manager()
    mapping_engine = DataMappingEngine()
    difference_analyzer = DifferenceAnalyzer()

    # 创建并启动注册服务
    registration_service = RuleRegistrationService()
    logger.info(f"[RegistrationWorker-{worker_id}] RuleRegistrationService starting...")

    try:
        await registration_service.start()
        logger.info(f"[RegistrationWorker-{worker_id}] RuleRegistrationService started successfully")
    except Exception as e:
        logger.error(f"[RegistrationWorker-{worker_id}] failed to start RuleRegistrationService: {e}", exc_info=True)
        raise  # 重新抛出异常，阻止worker启动

    try:
        while True:
            try:
                # 从注册队列获取任务
                item = await REGISTRATION_QUEUE.get()

                # 检查关闭信号
                if item is None:
                    logger.info(f"[RegistrationWorker-{worker_id}] received sentinel. Shutting down.")
                    break

                task_id, registration_data = item
                logger.info(f"[RegistrationWorker-{worker_id}] processing task: {task_id}")

                try:
                    await process_registration_task(
                        task_id,
                        registration_data,
                        task_manager,
                        registration_service,
                        mapping_engine,
                        difference_analyzer,
                    )
                except Exception as e:
                    logger.error(
                        f"[RegistrationWorker-{worker_id}] error processing task {task_id}: {e}", exc_info=True
                    )
                    # 更新任务状态为失败
                    await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
                finally:
                    REGISTRATION_QUEUE.task_done()

            except asyncio.CancelledError:
                logger.info(f"[RegistrationWorker-{worker_id}] was cancelled. Shutting down.")
                break
            except Exception as e:
                logger.error(f"[RegistrationWorker-{worker_id}] unexpected error: {e}", exc_info=True)
    finally:
        # 确保服务正确关闭
        logger.info(f"[RegistrationWorker-{worker_id}] RuleRegistrationService closing...")
        try:
            await registration_service.close()
            logger.info(f"[RegistrationWorker-{worker_id}] RuleRegistrationService closed successfully")
        except Exception as e:
            logger.error(f"[RegistrationWorker-{worker_id}] error closing RuleRegistrationService: {e}", exc_info=True)


async def process_registration_task(
    task_id: str,
    registration_data: dict[str, Any],
    task_manager,
    registration_service: RuleRegistrationService,
    mapping_engine: DataMappingEngine,
    difference_analyzer: DifferenceAnalyzer,
):
    """
    处理单个注册任务

    支持智能分批处理和传统处理两种模式，通过配置开关控制。
    智能分批处理模式提供自适应批次大小调整、性能监控和内存优化。

    Args:
        task_id: 任务ID
        registration_data: 注册数据，包含rule_key, excel_data, base_rule等
        task_manager: 任务状态管理器
        registration_service: 注册服务
        mapping_engine: 数据映射引擎
        difference_analyzer: 差异分析器
    """
    try:
        # 更新任务状态为运行中
        await task_manager.update_task_status(task_id, TaskStatus.RUNNING, message="开始处理注册任务")

        # 检查是否支持事务步骤跟踪
        supports_transaction_steps = hasattr(task_manager, "update_transaction_step")

        rule_key = registration_data.get("rule_key")
        excel_data = registration_data.get("excel_data", [])
        base_rule = registration_data.get("base_rule")

        # 检查是否启用智能分批处理
        from config.settings import settings

        adaptive_batch_enabled = getattr(settings, "RULE_REGISTRATION_ADAPTIVE_BATCH_ENABLED", False)
        ab_test_enabled = getattr(settings, "RULE_REGISTRATION_AB_TEST_ENABLED", False)
        ab_test_ratio = getattr(settings, "RULE_REGISTRATION_AB_TEST_RATIO", 0.5)

        # A/B测试决策（基于任务ID哈希）
        use_adaptive_batch = adaptive_batch_enabled
        if ab_test_enabled and adaptive_batch_enabled:
            import hashlib

            task_hash = int(hashlib.md5(task_id.encode()).hexdigest(), 16)
            use_adaptive_batch = (task_hash % 100) < (ab_test_ratio * 100)

        processing_mode = "adaptive_batch" if use_adaptive_batch else "traditional"
        logger.info(
            f"任务 {task_id}: 开始处理规则 '{rule_key}' 的注册，数据量: {len(excel_data)}，"
            f"处理模式: {processing_mode}"
        )

        # 步骤1: 数据映射转换
        await task_manager.update_task_progress(task_id, 0, "正在进行数据映射转换...")

        mapped_data = mapping_engine.map_to_registration_format(excel_data, base_rule, "UPSERT")

        logger.info(f"任务 {task_id}: 数据映射完成，映射后数据量: {len(mapped_data)}")

        # 步骤2: 差异分析
        await task_manager.update_task_progress(task_id, 1, "正在进行差异分析...")

        from core.db_session import get_session_factory

        session_factory = get_session_factory()

        with session_factory() as session:
            diff_result = difference_analyzer.analyze_data_diff(rule_key, mapped_data, session)

        delete_operations = diff_result["delete_operations"]
        upsert_operations = diff_result["upsert_operations"]
        total_operations = len(delete_operations) + len(upsert_operations)

        logger.info(f"任务 {task_id}: 差异分析完成，删除: {len(delete_operations)}, 更新: {len(upsert_operations)}")

        # 更新任务总操作数
        await task_manager.update_task_progress(task_id, 2, f"差异分析完成，共需处理 {total_operations} 个操作")

        # 标记数据库保存步骤完成（数据已经在confirm_submission中保存）
        if supports_transaction_steps:
            from services.persistent_task_status_manager import TransactionStep

            await task_manager.update_transaction_step(
                task_id, TransactionStep.DATABASE_SAVE, "completed", {"operations_count": total_operations}
            )

        # 步骤3: 执行注册操作
        if total_operations == 0:
            # 标记外部注册步骤完成（无需注册）
            if supports_transaction_steps:
                await task_manager.update_transaction_step(
                    task_id, TransactionStep.EXTERNAL_REGISTER, "completed", {"reason": "no_operations_needed"}
                )

            await task_manager.update_task_status(
                task_id, TaskStatus.COMPLETED, message="无需执行注册操作，数据已是最新"
            )
            await task_manager.set_task_result(
                task_id, {"success": True, "message": "无需执行注册操作", "operations_count": 0}
            )
            return

        # 根据处理模式选择执行路径
        if use_adaptive_batch:
            # 智能分批处理模式
            logger.info(f"任务 {task_id}: 使用智能分批处理模式执行注册操作")
            result = await _process_registration_with_adaptive_batch(
                task_id,
                delete_operations,
                upsert_operations,
                registration_service,
                task_manager,
                supports_transaction_steps,
            )
        else:
            # 传统处理模式
            logger.info(f"任务 {task_id}: 使用传统处理模式执行注册操作")
            result = await _process_registration_traditional(
                task_id,
                delete_operations,
                upsert_operations,
                registration_service,
                task_manager,
                supports_transaction_steps,
            )

        # 处理结果统一处理
        completed_ops = result["completed_ops"]  # noqa: F841
        successful_ops = result["successful_ops"]
        failed_ops = result["failed_ops"]
        external_register_success = result["external_register_success"]

        # 注册操作已在上面的分支中处理完成

        # 更新外部注册步骤状态
        if supports_transaction_steps:
            if external_register_success:
                await task_manager.update_transaction_step(
                    task_id,
                    TransactionStep.EXTERNAL_REGISTER,
                    "completed",
                    {
                        "successful_operations": successful_ops,
                        "failed_operations": failed_ops,
                        "delete_count": len(delete_operations),
                        "upsert_count": len(upsert_operations),
                    },
                )
            else:
                await task_manager.update_transaction_step(
                    task_id,
                    TransactionStep.EXTERNAL_REGISTER,
                    "failed",
                    {
                        "successful_operations": successful_ops,
                        "failed_operations": failed_ops,
                        "error_details": "部分或全部注册操作失败",
                    },
                )

                # 标记需要补偿操作
                if hasattr(task_manager, "mark_compensation_needed"):
                    await task_manager.mark_compensation_needed(
                        task_id, f"外部注册失败，成功: {successful_ops}, 失败: {failed_ops}"
                    )
                    logger.warning(f"任务 {task_id} 已标记需要补偿操作，外部注册失败")
        else:
            # 即使不支持事务步骤跟踪，也要在外部注册失败时记录警告
            if not external_register_success:
                logger.warning(f"任务 {task_id} 外部注册失败，但当前任务管理器不支持补偿标记")

        # 更新统计信息
        await task_manager.update_task_stats(
            task_id,
            {
                "delete_operations": len(delete_operations),
                "upsert_operations": len(upsert_operations),
                "successful_operations": successful_ops,
                "failed_operations": failed_ops,
            },
        )

        # 完成任务
        if failed_ops == 0:
            # 标记状态更新步骤完成
            if supports_transaction_steps:
                await task_manager.update_transaction_step(
                    task_id, TransactionStep.STATUS_UPDATE, "completed", {"final_status": "completed"}
                )

            await task_manager.update_task_status(
                task_id, TaskStatus.COMPLETED, message=f"注册任务完成，成功处理 {successful_ops} 个操作"
            )
            await task_manager.set_task_result(
                task_id,
                {
                    "success": True,
                    "message": "注册任务完成",
                    "operations_count": successful_ops,
                    "delete_count": len(delete_operations),
                    "upsert_count": len(upsert_operations),
                },
            )
        else:
            # 标记状态更新步骤完成（但任务失败）
            if supports_transaction_steps:
                await task_manager.update_transaction_step(
                    task_id,
                    TransactionStep.STATUS_UPDATE,
                    "completed",
                    {"final_status": "failed", "failed_operations": failed_ops},
                )

            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                message=f"注册任务部分失败，成功: {successful_ops}, 失败: {failed_ops}",
                error_message=f"有 {failed_ops} 个操作失败",
            )
            await task_manager.set_task_result(
                task_id,
                {
                    "success": False,
                    "message": "注册任务部分失败",
                    "operations_count": successful_ops,
                    "failed_count": failed_ops,
                },
            )

        logger.info(f"任务 {task_id}: 注册任务处理完成")

    except Exception as e:
        logger.error(f"任务 {task_id}: 处理过程中发生异常: {e}", exc_info=True)
        await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))


async def _process_registration_with_adaptive_batch(
    task_id: str,
    delete_operations: list[dict[str, Any]],
    upsert_operations: list[dict[str, Any]],
    registration_service,
    task_manager,
    supports_transaction_steps: bool,
) -> dict[str, Any]:
    """
    使用智能分批处理模式执行注册操作

    Args:
        task_id: 任务ID
        delete_operations: 删除操作列表
        upsert_operations: 更新操作列表
        registration_service: 注册服务
        task_manager: 任务管理器
        supports_transaction_steps: 是否支持事务步骤跟踪

    Returns:
        处理结果字典
    """
    import time

    from core.performance_monitor import performance_monitor
    from services.intelligent_batch_processor import intelligent_batch_processor

    start_time = time.time()
    completed_ops = 0
    successful_ops = 0
    failed_ops = 0
    external_register_success = True

    try:
        # 合并所有操作
        all_operations = []
        if delete_operations:
            all_operations.extend([{"type": "DELETE", "data": op} for op in delete_operations])
        if upsert_operations:
            all_operations.extend([{"type": "UPSERT", "data": op} for op in upsert_operations])

        if not all_operations:
            return {"completed_ops": 0, "successful_ops": 0, "failed_ops": 0, "external_register_success": True}

        logger.info(f"任务 {task_id}: 开始智能分批处理，总操作数: {len(all_operations)}")

        # 定义批处理函数
        async def process_batch(batch_operations):
            """处理单个批次的操作"""
            batch_delete_ops = [op["data"] for op in batch_operations if op["type"] == "DELETE"]
            batch_upsert_ops = [op["data"] for op in batch_operations if op["type"] == "UPSERT"]

            batch_results = []

            # 处理DELETE操作
            if batch_delete_ops:
                try:
                    delete_result = await registration_service.register_rules(batch_delete_ops)
                    batch_results.append(
                        {
                            "type": "DELETE",
                            "count": len(batch_delete_ops),
                            "success": delete_result.get("success", False),
                            "result": delete_result,
                        }
                    )
                except Exception as e:
                    batch_results.append(
                        {"type": "DELETE", "count": len(batch_delete_ops), "success": False, "error": str(e)}
                    )

            # 处理UPSERT操作
            if batch_upsert_ops:
                try:
                    upsert_result = await registration_service.register_rules(batch_upsert_ops)
                    batch_results.append(
                        {
                            "type": "UPSERT",
                            "count": len(batch_upsert_ops),
                            "success": upsert_result.get("success", False),
                            "result": upsert_result,
                        }
                    )
                except Exception as e:
                    batch_results.append(
                        {"type": "UPSERT", "count": len(batch_upsert_ops), "success": False, "error": str(e)}
                    )

            return batch_results

        # 使用智能分批处理器处理操作
        batch_count = 0
        async for batch_result in intelligent_batch_processor.process_registration_data_adaptively(
            all_operations, process_batch, session_id=task_id, enable_streaming=True
        ):
            batch_count += 1

            if batch_result.success:
                # 处理成功的批次结果
                for result in batch_result.processed_items if hasattr(batch_result, "processed_items") else []:
                    if isinstance(result, dict) and result.get("success"):
                        successful_ops += result.get("count", 0)
                    else:
                        failed_ops += result.get("count", 0)
                        external_register_success = False

                completed_ops += batch_result.batch_size

                # 更新进度
                progress = min(100, int((completed_ops / len(all_operations)) * 100))
                await task_manager.update_task_progress(
                    task_id,
                    progress,
                    f"智能分批处理进行中... 批次 {batch_count}, " f"已处理: {completed_ops}/{len(all_operations)}",
                )

                logger.debug(
                    f"任务 {task_id}: 批次 {batch_count} 处理成功，"
                    f"批次大小: {batch_result.batch_size}, "
                    f"处理时间: {batch_result.processing_time:.3f}s"
                )
            else:
                # 处理失败的批次
                failed_ops += batch_result.batch_size
                external_register_success = False
                completed_ops += batch_result.batch_size

                logger.error(
                    f"任务 {task_id}: 批次 {batch_count} 处理失败，"
                    f"批次大小: {batch_result.batch_size}, "
                    f"错误: {batch_result.error_message}"
                )

        # 记录性能指标
        processing_time = time.time() - start_time
        performance_monitor.record_registration_task_performance(
            task_id=f"{task_id}_adaptive_batch",
            batch_size=len(all_operations),
            processing_time=processing_time,
            success=external_register_success,
        )

        logger.info(
            f"任务 {task_id}: 智能分批处理完成，"
            f"总批次: {batch_count}, 成功: {successful_ops}, 失败: {failed_ops}, "
            f"总耗时: {processing_time:.3f}s"
        )

    except Exception as e:
        logger.error(f"任务 {task_id}: 智能分批处理异常: {e}", exc_info=True)
        failed_ops = len(all_operations)
        external_register_success = False

    return {
        "completed_ops": completed_ops,
        "successful_ops": successful_ops,
        "failed_ops": failed_ops,
        "external_register_success": external_register_success,
    }


async def _process_registration_traditional(
    task_id: str,
    delete_operations: list[dict[str, Any]],
    upsert_operations: list[dict[str, Any]],
    registration_service,
    task_manager,
    supports_transaction_steps: bool,
) -> dict[str, Any]:
    """
    使用传统处理模式执行注册操作（原有逻辑）

    Args:
        task_id: 任务ID
        delete_operations: 删除操作列表
        upsert_operations: 更新操作列表
        registration_service: 注册服务
        task_manager: 任务管理器
        supports_transaction_steps: 是否支持事务步骤跟踪

    Returns:
        处理结果字典
    """
    import time

    from core.performance_monitor import performance_monitor

    start_time = time.time()
    completed_ops = 0
    successful_ops = 0
    failed_ops = 0
    external_register_success = True

    try:
        # 执行DELETE操作
        if delete_operations:
            await task_manager.update_task_progress(
                task_id, completed_ops, f"正在执行 {len(delete_operations)} 个删除操作..."
            )

            try:
                delete_result = await registration_service.register_rules(delete_operations)
                if delete_result.get("success"):
                    successful_ops += len(delete_operations)
                    logger.info(f"任务 {task_id}: DELETE操作成功")
                else:
                    failed_ops += len(delete_operations)
                    external_register_success = False
                    logger.error(f"任务 {task_id}: DELETE操作失败: {delete_result}")
            except Exception as e:
                failed_ops += len(delete_operations)
                external_register_success = False
                logger.error(f"任务 {task_id}: DELETE操作异常: {e}")

            completed_ops += len(delete_operations)

        # 执行UPSERT操作
        if upsert_operations:
            await task_manager.update_task_progress(
                task_id, completed_ops, f"正在执行 {len(upsert_operations)} 个更新操作..."
            )

            try:
                upsert_result = await registration_service.register_rules(upsert_operations)
                if upsert_result.get("success"):
                    successful_ops += len(upsert_operations)
                    logger.info(f"任务 {task_id}: UPSERT操作成功")
                else:
                    failed_ops += len(upsert_operations)
                    external_register_success = False
                    logger.error(f"任务 {task_id}: UPSERT操作失败: {upsert_result}")
            except Exception as e:
                failed_ops += len(upsert_operations)
                external_register_success = False
                logger.error(f"任务 {task_id}: UPSERT操作异常: {e}")

            completed_ops += len(upsert_operations)

        # 记录性能指标
        processing_time = time.time() - start_time
        performance_monitor.record_registration_task_performance(
            task_id=f"{task_id}_traditional",
            batch_size=len(delete_operations) + len(upsert_operations),
            processing_time=processing_time,
            success=external_register_success,
        )

        logger.info(
            f"任务 {task_id}: 传统处理完成，"
            f"成功: {successful_ops}, 失败: {failed_ops}, "
            f"总耗时: {processing_time:.3f}s"
        )

    except Exception as e:
        logger.error(f"任务 {task_id}: 传统处理异常: {e}", exc_info=True)
        failed_ops = len(delete_operations) + len(upsert_operations)
        external_register_success = False

    return {
        "completed_ops": completed_ops,
        "successful_ops": successful_ops,
        "failed_ops": failed_ops,
        "external_register_success": external_register_success,
    }


# --- Offline Queue Processor ---
async def offline_queue_processor():
    """
    离线注册队列处理器

    定期检查并处理离线注册队列中的待处理项目，
    在外部服务恢复后自动重试失败的注册请求。
    """
    logger.info("离线队列处理器启动")

    # 处理间隔（秒）
    process_interval = 300  # 5分钟

    while True:
        try:
            # 等待处理间隔
            await asyncio.sleep(process_interval)

            # 创建注册服务实例进行队列处理
            registration_service = RuleRegistrationService()

            try:
                # 处理离线队列
                result = await registration_service.process_offline_queue()

                if result["processed"] > 0:
                    logger.info(
                        f"离线队列处理完成 - "
                        f"处理: {result['processed']}, "
                        f"成功: {result['successful']}, "
                        f"失败: {result['failed']}, "
                        f"剩余: {result['remaining']}"
                    )
                else:
                    logger.debug("离线队列为空，无需处理")

            finally:
                await registration_service.close()

        except asyncio.CancelledError:
            logger.info("离线队列处理器被取消，正在关闭")
            break
        except Exception as e:
            logger.error(f"离线队列处理器异常: {e}", exc_info=True)
            # 发生异常时等待较短时间后重试
            await asyncio.sleep(60)

    logger.info("离线队列处理器已关闭")


# --- Application Lifecycle (Startup/Shutdown) ---
@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Manages application startup and shutdown events."""
    global \
        rule_service, \
        WORKER_TASKS, \
        REQUEST_QUEUE, \
        REGISTRATION_QUEUE, \
        REGISTRATION_WORKER_TASKS, \
        OFFLINE_QUEUE_PROCESSOR_TASK
    logger.info("Master node starting up...")

    # 0. 规则注册配置验证
    logger.info("Validating rule registration configuration...")
    is_valid, errors, warnings = validate_rule_registration_config()

    if not is_valid:
        logger.critical("规则注册配置验证失败:")
        for error in errors:
            logger.critical(f"  - {error}")
        raise RuntimeError(f"规则注册配置验证失败: {'; '.join(errors)}")

    if warnings:
        logger.warning("规则注册配置警告:")
        for warning in warnings:
            logger.warning(f"  - {warning}")

    logger.info("规则注册配置验证通过")

    # 1. 数据库初始化检查（仅主节点需要）
    if settings.MODE == "master":
        logger.info("Initializing database...")
        db_info = get_database_info()
        logger.info(f"Database configuration: {db_info}")

        # 检查并初始化数据库
        success, message = initialize_database()
        if not success:
            logger.critical(f"Database initialization failed: {message}")
            raise RuntimeError(f"Database initialization failed: {message}")

        logger.info(f"Database initialization successful: {message}")

    # 2. Initialize services and queues
    REQUEST_QUEUE = asyncio.Queue(maxsize=settings.QUEUE_MAX_SIZE)
    REGISTRATION_QUEUE = asyncio.Queue(maxsize=settings.RULE_REGISTRATION_QUEUE_MAX_SIZE)
    rule_service = RuleService()

    # Start the rule service (including dynamic process pool)
    await rule_service.start()

    # Set dependencies for routers
    set_request_queue(REQUEST_QUEUE)
    set_rule_service(rule_service)

    logger.info("RuleService, request queue and registration queue initialized.")

    # Start database connection pool monitoring (仅主节点需要)
    if settings.MODE == "master":
        start_db_pool_monitoring()
        logger.info("Database connection pool monitoring started.")

        # Start monitoring service (仅主节点需要)
        if monitoring_config.is_monitoring_enabled():
            await monitoring_service.start()
            logger.info("Monitoring service started.")

    # 2. Run master-specific startup tasks (sync)
    if settings.MODE == "master":
        logger.info("Running master node startup tasks...")
        session_factory = get_session_factory()
        with session_factory() as session:
            # 步骤1: 规则变更检测
            logger.info("步骤1: 开始规则变更检测...")
            detector = RuleChangeDetector(db_session=session)
            detector.run()
            logger.info("规则变更检测完成")

            # 步骤2: 字段元数据初始化
            logger.info("步骤2: 开始字段元数据初始化...")
            try:
                from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer
                from tools.field_mapping_manager import FieldMappingManager

                # 创建字段映射管理器
                field_mapping_manager = FieldMappingManager("data/field_mapping.json")

                # 创建字段元数据初始化器
                metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)

                # 执行增量初始化（避免重复创建已存在的数据）
                init_result = metadata_initializer.initialize_all_metadata(mode="incremental")

                logger.info(
                    f"字段元数据初始化完成: 创建模板={init_result.get('created_templates', 0)}, "
                    f"更新模板={init_result.get('updated_templates', 0)}, "
                    f"创建字段元数据={init_result.get('created_field_metadata', 0)}, "
                    f"耗时={init_result.get('duration', 0):.2f}秒"
                )

                if init_result.get("total_errors", 0) > 0:
                    logger.warning(f"字段元数据初始化存在 {init_result['total_errors']} 个错误")
                    for error in init_result.get("errors", []):
                        logger.error(f"初始化错误: {error}")

            except Exception as e:
                logger.error(f"字段元数据初始化失败: {e}", exc_info=True)
                # 不阻止应用启动，但记录严重错误
                logger.warning("字段元数据初始化失败，模板生成可能受影响")

            # 步骤3: Excel模板预生成
            logger.info("步骤3: 开始预生成Excel模板...")
            try:
                from services.template_pre_generation_service import TemplatePreGenerationService

                template_service = TemplatePreGenerationService(session)
                result = await template_service.generate_all_templates_on_startup()

                logger.info(
                    f"Excel模板预生成完成: 总数={result['total']}, "
                    f"生成={result['generated']}, 跳过={result['skipped']}, "
                    f"失败={result['failed']}, 耗时={result['duration']:.2f}s"
                )

                # 如果有失败的模板，记录详细信息
                if result["failed"] > 0:
                    failed_templates = [d for d in result["details"] if d["status"] == "failed"]
                    for failed in failed_templates:
                        logger.error(f"模板生成失败: {failed['rule_key']}, error: {failed.get('error', 'Unknown')}")

            except Exception as e:
                logger.error(f"Excel模板预生成过程中发生异常: {e}", exc_info=True)
                # 不阻止应用启动，仅记录错误

    # 3. Load rules into cache
    logger.info("Loading rules into memory cache...")
    load_rules_into_cache()

    # 4. Generate rules cache file for slave nodes
    logger.info("Generating rules cache file for slave nodes...")
    if generate_rules_cache_file():
        logger.info("Rules cache file generated successfully.")
    else:
        logger.warning("Failed to generate rules cache file, slave nodes may need to sync from API.")

    # 5. Initialize degradation adapters for object pools
    try:
        from core.object_pool import get_object_pool_manager

        pool_manager = get_object_pool_manager()
        pool_manager.initialize_degradation_adapters()
    except Exception as e:
        logger.warning(f"Failed to initialize object pool degradation adapters: {e}")

    # 6. Start unified validation workers
    logger.info(f"Starting {settings.WORKER_COUNT} validation workers...")
    WORKER_TASKS = [
        asyncio.create_task(unified_validation_worker(REQUEST_QUEUE, rule_service, f"master-worker-{i}"))
        for i in range(settings.WORKER_COUNT)
    ]

    # 7. Start registration workers
    logger.info(f"Starting {settings.REGISTRATION_WORKER_COUNT} registration workers...")
    REGISTRATION_WORKER_TASKS = [
        asyncio.create_task(registration_worker(i)) for i in range(settings.REGISTRATION_WORKER_COUNT)
    ]

    # 8. Start offline queue processor
    logger.info("Starting offline registration queue processor...")
    OFFLINE_QUEUE_PROCESSOR_TASK = asyncio.create_task(offline_queue_processor())

    # 9. Start task status manager cleanup
    task_manager = get_task_status_manager()
    await task_manager.start_cleanup_task()

    # 10. Recover unfinished tasks (if using database storage)
    try:
        from services.task_recovery_service import get_task_recovery_service

        recovery_service = get_task_recovery_service()
        recovery_result = await recovery_service.recover_tasks_on_startup()

        if recovery_result.get("enabled"):
            logger.info(f"任务恢复完成: {recovery_result}")
        else:
            logger.debug(f"任务恢复: {recovery_result.get('message', '未启用')}")
    except Exception as e:
        logger.error(f"任务恢复过程中发生异常: {e}")

    logger.info("Startup process complete.")
    yield

    # Shutdown process
    logger.info("Master node shutting down...")

    # 1. Stop the RuleService first (including dynamic process pool)
    if rule_service:
        await rule_service.stop()
        rule_service.close()
        logger.info("Rule service stopped.")

    # 2. Stop the unified validation workers
    logger.info("Stopping validation workers...")
    for task in WORKER_TASKS:
        task.cancel()

    # Wait for all worker tasks to finish
    if WORKER_TASKS:
        await asyncio.gather(*WORKER_TASKS, return_exceptions=True)
    logger.info("All validation workers stopped.")

    # 3. Stop registration workers
    logger.info("Sending shutdown sentinels to registration workers...")
    for _ in REGISTRATION_WORKER_TASKS:
        try:
            REGISTRATION_QUEUE.put_nowait(None)
        except asyncio.QueueFull:
            logger.warning("Registration queue was full when trying to send shutdown sentinel.")

    # Wait for all registration worker tasks to finish
    await asyncio.gather(*REGISTRATION_WORKER_TASKS, return_exceptions=True)
    logger.info("All registration workers stopped.")

    # 4. Stop offline queue processor
    if OFFLINE_QUEUE_PROCESSOR_TASK:
        logger.info("Stopping offline queue processor...")
        OFFLINE_QUEUE_PROCESSOR_TASK.cancel()
        try:
            await OFFLINE_QUEUE_PROCESSOR_TASK
        except asyncio.CancelledError:
            pass
        logger.info("Offline queue processor stopped.")

    # 5. Stop task status manager cleanup
    task_manager = get_task_status_manager()
    await task_manager.stop_cleanup_task()
    logger.info("Task status manager cleanup stopped.")

    # Stop database connection pool monitoring (仅主节点需要)
    if settings.MODE == "master":
        # Stop monitoring service
        if monitoring_config.is_monitoring_enabled():
            await monitoring_service.stop()
            logger.info("Monitoring service stopped.")

        stop_db_pool_monitoring()
        logger.info("Database connection pool monitoring stopped.")

    logger.info("Shutdown process complete.")


# --- FastAPI App Initialization ---
def create_master_app() -> FastAPI:
    """
    Create and configure the master FastAPI application.

    Returns:
        FastAPI: Configured master application
    """
    app = FastAPI(
        title="Rule Master Node",
        description="Manages the entire lifecycle of rules and serves them to slave nodes, also provides validation endpoint.",  # noqa: E501
        version="1.0.0",
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 前端开发服务器地址
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add exception handlers
    error_handlers = UnifiedErrorHandlingMiddleware.get_handlers()
    for exception_type, handler in error_handlers.items():
        app.add_exception_handler(exception_type, handler)

    # Include all master routers
    for router in master_routers:
        app.include_router(router)

    # Include monitoring API router
    if monitoring_config.is_monitoring_enabled():
        from api.monitoring_api import router as monitoring_router

        app.include_router(monitoring_router)

    return app


# Create the app instance
app = create_master_app()


if __name__ == "__main__":
    uvicorn.run("master:app", host="0.0.0.0", port=8000, reload=True)
