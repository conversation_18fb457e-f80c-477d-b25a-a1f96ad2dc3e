"""
子节点数据加载重构简化集成测试
专门用于测试核心加载功能，避免复杂依赖
"""

import asyncio
import gzip
import json
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from core.rule_cache import RULE_CACHE
from services.rule_loader import _clear_cache_stats


class TestRuleLoaderSimpleIntegration(unittest.TestCase):
    """简化的规则加载器集成测试"""

    def setUp(self):
        """测试前准备"""
        # 清空规则缓存
        RULE_CACHE.clear()
        
        # 清空性能统计数据
        _clear_cache_stats()

        # 创建临时测试文件
        self.temp_dir = tempfile.mkdtemp()
        self.test_cache_file = os.path.join(self.temp_dir, "test_rules_cache.json.gz")
        self.test_version_file = os.path.join(self.temp_dir, "test_rules_version.txt")

        # 创建测试数据
        self.create_test_cache_files()

    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

        # 清空缓存
        RULE_CACHE.clear()
        
        # 清空性能统计数据
        _clear_cache_stats()

    def create_test_cache_files(self):
        """创建测试用的缓存文件"""
        # v2.0格式测试数据
        v2_data = {
            "version": "v2.0",
            "metadata": {"export_timestamp": "2025-07-27T10:00:00Z", "total_templates": 2, "total_details": 3},
            "templates": [
                {
                    "rule_template_id": "template_1",
                    "rule_key": "test_rule_1",
                    "rule_name": "测试规则1",
                    "status": "active",
                },
                {
                    "rule_template_id": "template_2",
                    "rule_key": "test_rule_2",
                    "rule_name": "测试规则2",
                    "status": "active",
                },
            ],
            "details": [
                {
                    "rule_detail_id": "detail_1",
                    "rule_key": "test_rule_1",
                    "level1": "错误",
                    "level2": "数据",
                    "level3": "缺失",
                    "error_reason": "必填字段为空",
                },
                {
                    "rule_detail_id": "detail_2",
                    "rule_key": "test_rule_1",
                    "level1": "警告",
                    "level2": "格式",
                    "level3": "异常",
                    "error_reason": "日期格式不正确",
                },
                {
                    "rule_detail_id": "detail_3",
                    "rule_key": "test_rule_2",
                    "level1": "错误",
                    "level2": "逻辑",
                    "level3": "冲突",
                    "error_reason": "业务逻辑冲突",
                },
            ],
            "field_metadata": [
                {"field_id": "field_1", "field_name": "level1", "field_type": "string", "is_required": True}
            ],
        }

        # 创建v2.0格式的gzip压缩文件
        with gzip.open(self.test_cache_file, "wt", encoding="utf-8") as f:
            json.dump(v2_data, f, ensure_ascii=False, indent=2)

        # 创建版本文件
        with open(self.test_version_file, "w") as f:
            f.write("v2.0_test_20250727")

    def test_cache_format_detection_v2(self):
        """测试v2.0缓存格式检测"""
        # 直接导入函数来避免复杂的模块加载问题
        from services.rule_loader import _detect_cache_format

        format_info = _detect_cache_format(self.test_cache_file)

        self.assertTrue(format_info["is_valid"])
        self.assertEqual(format_info["format_version"], "v2.0")
        self.assertEqual(format_info["format_type"], "sync_service")
        self.assertTrue(format_info["is_gzip"])
        self.assertGreaterEqual(format_info["detection_confidence"], 0.95)
        self.assertGreater(format_info["file_size_mb"], 0)

    def test_cache_freshness_check(self):
        """测试缓存新鲜度检查"""
        from services.rule_loader import _check_cache_freshness

        with patch("services.rule_loader.LOCAL_RULES_PATH", self.test_cache_file):
            with patch("services.rule_loader.LOCAL_VERSION_PATH", self.test_version_file):
                cache_info = _check_cache_freshness()

                self.assertTrue(cache_info["cache_exists"])
                self.assertTrue(cache_info["version_exists"])
                self.assertIsNotNone(cache_info["cache_version"])
                self.assertGreater(cache_info["cache_size_mb"], 0)

    def test_load_rules_from_file_with_mock(self):
        """测试使用Mock的规则文件加载"""
        from services.rule_loader import load_rules_from_file

        # 使用实际的缓存文件路径，模拟RuleDataSyncService
        with patch("services.rule_loader.LOCAL_RULES_PATH", self.test_cache_file):
            with patch("services.rule_loader.LOCAL_VERSION_PATH", self.test_version_file):
                with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                    mock_stats = Mock()
                    mock_stats.total_templates = 2
                    mock_stats.total_details = 3
                    mock_stats.sync_duration = 0.5
                    mock_stats.cache_size_mb = 1.2

                    mock_instance = Mock()
                    mock_instance.load_from_cache.return_value = mock_stats
                    mock_sync_service.return_value = mock_instance

                    # 执行测试
                    result = asyncio.run(load_rules_from_file())

                    self.assertTrue(result)
                    mock_sync_service.assert_called_once()
                    mock_instance.load_from_cache.assert_called_once()

    def test_performance_monitoring_functions(self):
        """测试性能监控功能"""
        from services.rule_loader import _get_cache_performance_report, _update_cache_stats

        # 更新统计数据
        _update_cache_stats(0.5, 10.5, "v2.0_test")
        _update_cache_stats(0.3, 9.8, "v2.0_test")

        # 获取性能报告
        report = _get_cache_performance_report()

        self.assertIn("avg_load_time", report)
        self.assertIn("avg_memory_usage", report)
        self.assertIn("total_loads", report)
        self.assertEqual(report["total_loads"], 2)

    def test_memory_optimization_function(self):
        """测试内存优化功能"""
        from services.rule_loader import _optimize_memory_usage_enhanced

        # 创建一些内存占用
        large_objects = [list(range(100)) for _ in range(10)]

        # 执行内存优化
        start_time = time.time()
        _optimize_memory_usage_enhanced()
        optimization_time = time.time() - start_time

        # 验证优化时间合理
        self.assertLess(optimization_time, 2.0)

        # 清理测试对象
        del large_objects

    def test_error_handling_file_not_found(self):
        """测试文件不存在的错误处理"""
        from services.rule_loader import load_rules_from_file

        non_existent_file = "/path/to/non/existent/file.json.gz"

        with patch("services.rule_loader.LOCAL_RULES_PATH", non_existent_file):
            result = asyncio.run(load_rules_from_file())
            self.assertFalse(result)

    def test_load_rules_into_cache_with_mock(self):
        """测试数据库加载集成（使用Mock）"""
        from services.rule_loader import load_rules_into_cache

        # 模拟数据库会话
        with patch("services.rule_loader.get_session_factory") as mock_session_factory:
            mock_session = Mock()
            mock_session_factory.return_value.return_value.__enter__ = Mock(return_value=mock_session)
            mock_session_factory.return_value.return_value.__exit__ = Mock(return_value=None)

            # 模拟RuleDataSyncService
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                mock_stats = Mock()
                mock_stats.total_templates = 5
                mock_stats.total_details = 10
                mock_stats.sync_duration = 1.2
                mock_stats.cache_size_mb = 2.5

                mock_instance = Mock()
                mock_instance.sync_from_database.return_value = mock_stats
                mock_sync_service.return_value = mock_instance

                # 执行测试
                load_rules_into_cache()

                mock_sync_service.assert_called_once()
                mock_instance.sync_from_database.assert_called_once()

    def test_corrupted_file_handling(self):
        """测试损坏文件的处理机制"""
        from services.rule_loader import load_rules_from_file

        corrupted_file = os.path.join(self.temp_dir, "corrupted.json.gz")

        # 创建损坏的文件
        with open(corrupted_file, "wb") as f:
            f.write(b"corrupted data that is not gzip")

        with patch("services.rule_loader.LOCAL_RULES_PATH", corrupted_file):
            result = asyncio.run(load_rules_from_file())

            # 应该返回False或处理错误
            self.assertIsNotNone(result)

    def test_large_cache_performance(self):
        """测试大型缓存性能"""
        from services.rule_loader import _detect_cache_format

        # 创建较大的测试数据
        large_data = {
            "version": "v2.0",
            "metadata": {"export_timestamp": "2025-07-27T10:00:00Z", "total_templates": 100, "total_details": 500},
            "templates": [
                {
                    "rule_template_id": f"template_{i}",
                    "rule_key": f"test_rule_{i}",
                    "rule_name": f"测试规则{i}",
                    "status": "active",
                }
                for i in range(100)
            ],
            "details": [
                {
                    "rule_detail_id": f"detail_{i}_{j}",
                    "rule_key": f"test_rule_{i}",
                    "level1": "错误" if j % 2 == 0 else "警告",
                    "level2": f"类型{j}",
                    "level3": f"子类型{j}",
                    "error_reason": f"测试错误原因{i}_{j}",
                }
                for i in range(100)
                for j in range(5)
            ],
        }

        large_cache_file = os.path.join(self.temp_dir, "large_cache.json.gz")
        with gzip.open(large_cache_file, "wt", encoding="utf-8") as f:
            json.dump(large_data, f, ensure_ascii=False)

        start_time = time.time()
        format_info = _detect_cache_format(large_cache_file)
        detection_time = time.time() - start_time

        self.assertTrue(format_info["is_valid"])
        self.assertEqual(format_info["format_version"], "v2.0")
        self.assertLess(detection_time, 1.0)  # 检测时间应小于1秒

        print(f"Large cache format detection time: {detection_time:.3f}s")
        print(f"Cache size: {format_info['file_size_mb']:.2f}MB")


if __name__ == "__main__":
    # 运行简化集成测试
    print("开始执行子节点数据加载重构简化集成测试")
    print("=" * 60)

    unittest.main(verbosity=2)

    print("=" * 60)
    print("简化集成测试执行完成")