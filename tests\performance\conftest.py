"""
性能测试专用配置
提供性能测试所需的fixtures和配置
"""

import time
from typing import Any

import psutil
import pytest

# ============================================================================
# 性能测试专用fixtures
# ============================================================================

@pytest.fixture
def performance_monitor():
    """性能监控器"""
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.start_memory = None
            self.end_memory = None

        def start(self):
            self.start_time = time.time()
            self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        def stop(self):
            self.end_time = time.time()
            self.end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        def get_metrics(self) -> dict[str, Any]:
            return {
                "execution_time": self.end_time - self.start_time if self.end_time and self.start_time else 0,
                "memory_usage": self.end_memory - self.start_memory if self.end_memory and self.start_memory else 0,
                "peak_memory": self.end_memory if self.end_memory else 0,
            }

    return PerformanceMonitor()


@pytest.fixture
def performance_thresholds():
    """性能测试阈值"""
    return {
        "max_response_time": 2.0,  # 最大响应时间（秒）
        "max_memory_usage": 200,   # 最大内存使用（MB）
        "max_cpu_usage": 90,       # 最大CPU使用率（%）
    }


# ============================================================================
# 性能测试配置
# ============================================================================

@pytest.fixture(autouse=True)
def performance_test_setup():
    """性能测试自动设置"""
    # 性能测试需要稳定的环境
    # 可能需要预热、清理缓存等操作
    yield
