"""
字段映射管理工具
负责统一管理字段映射配置，提供字段查询、转换、验证等功能
"""

import json
import logging
from pathlib import Path
from typing import Any

logger = logging.getLogger(__name__)


class FieldMappingError(Exception):
    """字段映射相关异常"""

    def __init__(self, message: str, field_name: str = None, details: dict = None):
        super().__init__(message)
        self.field_name = field_name
        self.details = details or {}


class FieldMappingManager:
    """
    字段映射管理工具

    负责统一管理字段映射配置，提供字段查询、转换、验证等功能。
    基于field_mapping.json配置文件，确保前后端字段命名的一致性。
    """

    def __init__(self, config_path: str = "data/field_mapping.json"):
        """
        初始化字段映射管理器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = None
        self._field_cache = {}
        self._rule_type_cache = {}
        self._load_config()

    def _load_config(self) -> None:
        """加载字段映射配置文件"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)

            self._build_field_cache()
            self._build_rule_type_cache()

            version = self.config.get("metadata", {}).get("version", "unknown")
            logger.info(f"字段映射配置加载成功，版本: {version}")

        except FileNotFoundError:
            error_msg = f"字段映射配置文件不存在: {self.config_path}"
            logger.error(error_msg)
            raise FieldMappingError(error_msg) from None
        except json.JSONDecodeError as e:
            error_msg = f"字段映射配置文件格式错误: {e}"
            logger.error(error_msg)
            raise FieldMappingError(error_msg) from None
        except Exception as e:
            error_msg = f"加载字段映射配置失败: {e}"
            logger.error(error_msg)
            raise FieldMappingError(error_msg) from None

    def _build_field_cache(self) -> None:
        """构建字段查询缓存"""
        self._field_cache.clear()

        field_definitions = self.config.get("field_definitions", {})
        common_fields = field_definitions.get("common_fields", {})
        specific_fields = field_definitions.get("specific_fields", {})

        # 合并所有字段定义到缓存中
        for field_name, field_def in {**common_fields, **specific_fields}.items():
            self._field_cache[field_name] = field_def

        logger.debug(f"字段缓存构建完成，共 {len(self._field_cache)} 个字段")

    def _build_rule_type_cache(self) -> None:
        """构建规则类型缓存"""
        self._rule_type_cache.clear()

        rule_type_mappings = self.config.get("rule_type_mappings", {})
        for rule_type, rule_info in rule_type_mappings.items():
            self._rule_type_cache[rule_type] = rule_info

        logger.debug(f"规则类型缓存构建完成，共 {len(self._rule_type_cache)} 种规则类型")

    def get_field_definition(self, field_name: str) -> dict[str, Any] | None:
        """
        获取字段定义

        Args:
            field_name: 字段名称

        Returns:
            字段定义字典，如果字段不存在则返回None
        """
        try:
            if field_name in self._field_cache:
                return self._field_cache[field_name].copy()

            logger.warning(f"字段 '{field_name}' 未在配置中找到")
            return None

        except Exception as e:
            logger.error(f"获取字段定义时发生错误: {e}")
            raise FieldMappingError(f"获取字段 '{field_name}' 定义失败", field_name=field_name) from None

    def get_chinese_name(self, field_name: str) -> str:
        """
        获取字段的中文名称

        Args:
            field_name: 字段名称

        Returns:
            字段的中文名称，如果未找到则返回原字段名
        """
        field_def = self.get_field_definition(field_name)
        if field_def:
            return field_def.get("chinese_name", field_name)
        return field_name

    def get_standard_field_name(self, field_name: str) -> str:
        """
        获取标准字段名（当前版本已移除别名支持，直接返回原字段名）

        Args:
            field_name: 字段名称

        Returns:
            标准字段名
        """
        # 检查字段是否存在于配置中
        if self.get_field_definition(field_name):
            return field_name

        # 如果字段不存在，记录警告并返回原字段名
        logger.warning(f"字段 '{field_name}' 不在标准配置中")
        return field_name

    def get_database_column(self, field_name: str) -> str:
        """
        获取数据库列名

        Args:
            field_name: 字段名称

        Returns:
            数据库列名
        """
        field_def = self.get_field_definition(field_name)
        if field_def:
            return field_def.get("database_column", field_name)
        return field_name

    def get_api_field(self, field_name: str) -> str:
        """
        获取API字段名

        Args:
            field_name: 字段名称

        Returns:
            API字段名
        """
        field_def = self.get_field_definition(field_name)
        if field_def:
            return field_def.get("api_field", field_name)
        return field_name

    def get_excel_column(self, field_name: str) -> str:
        """
        获取Excel列名

        Args:
            field_name: 字段名称

        Returns:
            Excel列名（通常是中文名称）
        """
        field_def = self.get_field_definition(field_name)
        if field_def:
            return field_def.get("excel_column", self.get_chinese_name(field_name))
        return self.get_chinese_name(field_name)

    def get_validation_rules(self, field_name: str) -> list[str]:
        """
        获取字段验证规则

        Args:
            field_name: 字段名称

        Returns:
            验证规则列表
        """
        field_def = self.get_field_definition(field_name)
        if field_def:
            return field_def.get("validation_rules", [])
        return []

    def get_rule_type_fields(self, rule_type: str) -> dict[str, list[str]]:
        """
        获取规则类型的字段配置

        Args:
            rule_type: 规则类型

        Returns:
            包含required和optional字段列表的字典
        """
        if rule_type in self._rule_type_cache:
            rule_info = self._rule_type_cache[rule_type]
            return {"required": rule_info.get("required_fields", []), "optional": rule_info.get("optional_fields", [])}

        logger.warning(f"规则类型 '{rule_type}' 未在配置中找到")
        return {"required": [], "optional": []}

    def get_all_field_names(self) -> list[str]:
        """
        获取所有字段名称

        Returns:
            所有字段名称列表
        """
        return list(self._field_cache.keys())

    def get_common_fields(self) -> dict[str, Any]:
        """
        获取通用字段定义

        Returns:
            通用字段定义字典
        """
        return self.config.get("field_definitions", {}).get("common_fields", {})

    def get_specific_fields(self) -> dict[str, Any]:
        """
        获取特定字段定义

        Returns:
            特定字段定义字典
        """
        return self.config.get("field_definitions", {}).get("specific_fields", {})

    def reload_config(self) -> None:
        """
        重新加载配置文件
        """
        try:
            self._load_config()
            logger.info("字段映射配置重新加载成功")
        except Exception as e:
            logger.error(f"配置重新加载失败: {e}")
            raise

    def get_config_info(self) -> dict[str, Any]:
        """
        获取配置信息摘要

        Returns:
            配置信息字典
        """
        metadata = self.config.get("metadata", {})
        return {
            "version": metadata.get("version", "unknown"),
            "last_updated": metadata.get("last_updated", "unknown"),
            "description": metadata.get("description", ""),
            "total_fields": len(self._field_cache),
            "common_fields_count": len(self.get_common_fields()),
            "specific_fields_count": len(self.get_specific_fields()),
            "rule_types_count": len(self._rule_type_cache),
            "config_path": str(self.config_path),
        }

    def generate_typescript_types(self) -> str:
        """
        生成TypeScript类型定义

        Returns:
            TypeScript类型定义字符串
        """
        common_fields = self.get_common_fields()
        specific_fields = self.get_specific_fields()

        ts_content = """// 自动生成的字段类型定义
// 请勿手动修改，运行字段映射管理工具重新生成

/**
 * 通用字段接口
 */
export interface CommonFields {
"""

        # 生成通用字段类型定义
        for field_name, field_def in common_fields.items():
            ts_type = self._get_typescript_type(field_def.get("data_type", "string"))
            required = "" if field_def.get("required", False) else "?"
            comment = f"  /** {field_def.get('chinese_name', field_name)} - {field_def.get('description', '')} */"
            ts_content += f"{comment}\n  {field_name}{required}: {ts_type}\n"

        ts_content += "}\n\n"

        # 生成特定字段接口
        ts_content += "/**\n * 特定字段接口\n */\nexport interface SpecificFields {\n"

        for field_name, field_def in specific_fields.items():
            ts_type = self._get_typescript_type(field_def.get("data_type", "string"))
            comment = f"  /** {field_def.get('chinese_name', field_name)} - {field_def.get('description', '')} */"
            ts_content += f"{comment}\n  {field_name}?: {ts_type}\n"

        ts_content += "}\n\n"

        # 生成完整规则详情接口
        ts_content += """/**
 * 完整规则详情接口
 */
export interface RuleDetail extends CommonFields {
  extended_fields?: SpecificFields
}

"""

        # 生成字段中文名称映射
        ts_content += "/**\n * 字段中文名称映射\n */\nexport const FIELD_CHINESE_NAMES = {\n"

        for field_name, field_def in {**common_fields, **specific_fields}.items():
            chinese_name = field_def.get("chinese_name", field_name)
            ts_content += f"  {field_name}: '{chinese_name}',\n"

        ts_content += "} as const\n\n"

        # 生成字段类型映射
        ts_content += "/**\n * 字段数据类型映射\n */\nexport const FIELD_DATA_TYPES = {\n"

        for field_name, field_def in {**common_fields, **specific_fields}.items():
            data_type = field_def.get("data_type", "string")
            ts_content += f"  {field_name}: '{data_type}',\n"

        ts_content += "} as const\n\n"

        # 生成工具函数
        ts_content += """/**
 * 获取字段的中文名称
 */
export function getFieldChineseName(fieldName: string): string {
  return FIELD_CHINESE_NAMES[fieldName] || fieldName
}

/**
 * 获取字段的数据类型
 */
export function getFieldDataType(fieldName: string): string {
  return FIELD_DATA_TYPES[fieldName] || 'string'
}

/**
 * 检查字段是否为必填字段
 */
export function isRequiredField(fieldName: string): boolean {
  // 这里可以根据需要扩展必填字段检查逻辑
  return false
}
"""

        return ts_content

    def _get_typescript_type(self, data_type: str) -> str:
        """
        转换数据类型为TypeScript类型

        Args:
            data_type: 数据类型

        Returns:
            TypeScript类型字符串
        """
        type_mapping = {
            "string": "string",
            "text": "string",
            "integer": "number",
            "array": "string[]",
            "boolean": "boolean",
        }
        return type_mapping.get(data_type, "any")

    def validate_field_usage(self, project_path: str) -> dict[str, list[str]]:
        """
        验证项目中字段使用的一致性

        Args:
            project_path: 项目路径

        Returns:
            验证结果字典，包含问题列表
        """
        issues = {"missing_fields": [], "unknown_fields": [], "inconsistent_usage": []}

        # 这里可以实现项目文件扫描逻辑
        # 由于涉及文件系统操作，暂时返回空结果
        logger.info(f"字段使用验证功能待实现，项目路径: {project_path}")

        return issues
