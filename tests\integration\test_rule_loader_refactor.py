"""
子节点数据加载重构集成测试
测试重构后的rule_loader.py与各组件的集成效果
验证功能正确性、性能表现和向后兼容性
"""

import asyncio
import gzip
import json
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from core.rule_cache import RULE_CACHE
from services.rule_loader import (
    LOCAL_RULES_PATH,
    _check_cache_freshness,
    _clear_cache_stats,
    _detect_cache_format,
    _get_cache_performance_report,
    _optimize_memory_usage_enhanced,
    _update_cache_stats,
    load_rules_from_file,
    load_rules_into_cache,
)


class TestRuleLoaderRefactorIntegration(unittest.TestCase):
    """子节点数据加载重构集成测试类"""

    def setUp(self):
        """测试前准备"""
        # 清空规则缓存
        RULE_CACHE.clear()
        
        # 清空性能统计数据
        _clear_cache_stats()

        # 创建临时测试文件
        self.temp_dir = tempfile.mkdtemp()
        self.test_cache_file = os.path.join(self.temp_dir, "test_rules_cache.json.gz")
        self.test_version_file = os.path.join(self.temp_dir, "test_rules_version.txt")

        # 备份原始路径
        self.original_cache_path = LOCAL_RULES_PATH

        # 创建测试数据
        self.create_test_cache_files()

    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

        # 清空缓存
        RULE_CACHE.clear()
        
        # 清空性能统计数据
        _clear_cache_stats()

    def create_test_cache_files(self):
        """创建测试用的缓存文件"""
        # v2.0格式测试数据
        v2_data = {
            "version": "v2.0",
            "metadata": {"export_timestamp": "2025-07-27T10:00:00Z", "total_templates": 2, "total_details": 3},
            "templates": [
                {
                    "rule_template_id": "template_1",
                    "rule_key": "test_rule_1",
                    "rule_name": "测试规则1",
                    "status": "active",
                },
                {
                    "rule_template_id": "template_2",
                    "rule_key": "test_rule_2",
                    "rule_name": "测试规则2",
                    "status": "active",
                },
            ],
            "details": [
                {
                    "rule_detail_id": "detail_1",
                    "rule_key": "test_rule_1",
                    "level1": "错误",
                    "level2": "数据",
                    "level3": "缺失",
                    "error_reason": "必填字段为空",
                },
                {
                    "rule_detail_id": "detail_2",
                    "rule_key": "test_rule_1",
                    "level1": "警告",
                    "level2": "格式",
                    "level3": "异常",
                    "error_reason": "日期格式不正确",
                },
                {
                    "rule_detail_id": "detail_3",
                    "rule_key": "test_rule_2",
                    "level1": "错误",
                    "level2": "逻辑",
                    "level3": "冲突",
                    "error_reason": "业务逻辑冲突",
                },
            ],
            "field_metadata": [
                {"field_id": "field_1", "field_name": "level1", "field_type": "string", "is_required": True}
            ],
        }

        # 创建v2.0格式的gzip压缩文件
        with gzip.open(self.test_cache_file, "wt", encoding="utf-8") as f:
            json.dump(v2_data, f, ensure_ascii=False, indent=2)

        # 创建版本文件
        with open(self.test_version_file, "w") as f:
            f.write("v2.0_test_20250727")

    def test_cache_format_detection_v2(self):
        """测试v2.0缓存格式检测"""
        format_info = _detect_cache_format(self.test_cache_file)

        self.assertTrue(format_info["is_valid"])
        self.assertEqual(format_info["format_version"], "v2.0")
        self.assertEqual(format_info["format_type"], "sync_service")
        self.assertTrue(format_info["is_gzip"])
        self.assertGreaterEqual(format_info["detection_confidence"], 0.95)
        self.assertGreater(format_info["file_size_mb"], 0)

    def test_load_rules_from_file_success(self):
        """测试成功加载规则文件"""

        # 使用实际的缓存文件路径，而不是Mock对象
        with patch("services.rule_loader.LOCAL_RULES_PATH", self.test_cache_file):
            with patch("services.rule_loader.LOCAL_VERSION_PATH", self.test_version_file):
                # 模拟RuleDataSyncService
                with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                    mock_stats = Mock()
                    mock_stats.total_templates = 2
                    mock_stats.total_details = 3
                    mock_stats.sync_duration = 0.5
                    mock_stats.cache_size_mb = 1.2

                    mock_instance = Mock()
                    mock_instance.load_from_cache.return_value = mock_stats
                    mock_sync_service.return_value = mock_instance

                    # 执行测试
                    result = asyncio.run(load_rules_from_file())

                    self.assertTrue(result)
                    mock_sync_service.assert_called_once()
                    mock_instance.load_from_cache.assert_called_once()

    def test_error_handling_file_not_found(self):
        """测试文件不存在的错误处理"""
        non_existent_file = "/path/to/non/existent/file.json.gz"

        with patch("services.rule_loader.LOCAL_RULES_PATH", non_existent_file):
            result = asyncio.run(load_rules_from_file())
            self.assertFalse(result)

    def test_cache_freshness_check(self):
        """测试缓存新鲜度检查"""
        with patch("services.rule_loader.LOCAL_RULES_PATH", self.test_cache_file):
            with patch("services.rule_loader.LOCAL_VERSION_PATH", self.test_version_file):
                cache_info = _check_cache_freshness()

                self.assertTrue(cache_info["cache_exists"])
                self.assertTrue(cache_info["version_exists"])
                self.assertIsNotNone(cache_info["cache_version"])
                self.assertGreater(cache_info["cache_size_mb"], 0)

    def test_performance_monitoring(self):
        """测试性能监控功能"""
        # 模拟一些缓存统计数据
        from services.rule_loader import _get_cache_performance_report, _update_cache_stats

        # 更新统计数据
        _update_cache_stats(0.5, 10.5, "v2.0_test")
        _update_cache_stats(0.3, 9.8, "v2.0_test")

        # 获取性能报告
        report = _get_cache_performance_report()

        self.assertIn("avg_load_time", report)
        self.assertIn("avg_memory_usage", report)
        self.assertIn("total_loads", report)
        self.assertEqual(report["total_loads"], 2)

    @patch("services.rule_loader.get_session_factory")
    def test_load_rules_into_cache_integration(self, mock_session_factory):
        """测试数据库加载集成"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session_factory.return_value.return_value.__enter__ = Mock(return_value=mock_session)
        mock_session_factory.return_value.return_value.__exit__ = Mock(return_value=None)

        # 模拟RuleDataSyncService
        with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
            mock_stats = Mock()
            mock_stats.total_templates = 5
            mock_stats.total_details = 10
            mock_stats.sync_duration = 1.2
            mock_stats.cache_size_mb = 2.5

            mock_instance = Mock()
            mock_instance.sync_from_database.return_value = mock_stats
            mock_sync_service.return_value = mock_instance

            # 执行测试
            load_rules_into_cache()

            mock_sync_service.assert_called_once()
            mock_instance.sync_from_database.assert_called_once()


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试类"""

    def setUp(self):
        """性能测试准备"""
        # 清空性能统计数据
        _clear_cache_stats()
        
        self.temp_dir = tempfile.mkdtemp()
        self.large_cache_file = os.path.join(self.temp_dir, "large_cache.json.gz")
        self.create_large_test_cache()

    def tearDown(self):
        """性能测试清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
        # 清空性能统计数据
        _clear_cache_stats()

    def create_large_test_cache(self):
        """创建大型测试缓存文件"""
        # 创建包含1000个模板和5000个详情的大型缓存
        large_data = {
            "version": "v2.0",
            "metadata": {"export_timestamp": "2025-07-27T10:00:00Z", "total_templates": 1000, "total_details": 5000},
            "templates": [],
            "details": [],
            "field_metadata": [],
        }

        # 生成测试模板
        for i in range(1000):
            template = {
                "rule_template_id": f"template_{i}",
                "rule_key": f"test_rule_{i}",
                "rule_name": f"测试规则{i}",
                "status": "active",
            }
            large_data["templates"].append(template)

        # 生成测试详情（每个模板5个详情）
        for i in range(1000):
            for j in range(5):
                detail = {
                    "rule_detail_id": f"detail_{i}_{j}",
                    "rule_key": f"test_rule_{i}",
                    "level1": "错误" if j % 2 == 0 else "警告",
                    "level2": f"类型{j}",
                    "level3": f"子类型{j}",
                    "error_reason": f"测试错误原因{i}_{j}",
                }
                large_data["details"].append(detail)

        # 保存为gzip压缩文件
        with gzip.open(self.large_cache_file, "wt", encoding="utf-8") as f:
            json.dump(large_data, f, ensure_ascii=False)

    def test_large_cache_format_detection_performance(self):
        """测试大型缓存格式检测性能"""
        start_time = time.time()

        format_info = _detect_cache_format(self.large_cache_file)

        detection_time = time.time() - start_time

        self.assertTrue(format_info["is_valid"])
        self.assertEqual(format_info["format_version"], "v2.0")
        self.assertLess(detection_time, 1.0)  # 检测时间应小于1秒

        print(f"Large cache format detection time: {detection_time:.3f}s")
        print(f"Cache size: {format_info['file_size_mb']:.2f}MB")

    def test_cache_freshness_check_performance(self):
        """测试缓存新鲜度检查性能"""
        with patch("services.rule_loader.LOCAL_RULES_PATH", self.large_cache_file):
            # 第一次检查（冷启动）
            start_time = time.time()
            cache_info1 = _check_cache_freshness()
            first_check_time = time.time() - start_time

            # 第二次检查（应该更快）
            start_time = time.time()
            cache_info2 = _check_cache_freshness()
            second_check_time = time.time() - start_time

        self.assertTrue(cache_info1["cache_exists"])
        self.assertTrue(cache_info2["cache_exists"])
        self.assertLess(first_check_time, 0.5)  # 首次检查应小于0.5秒
        self.assertLess(second_check_time, 0.1)  # 二次检查应小于0.1秒

        print(f"First cache freshness check: {first_check_time:.3f}s")
        print(f"Second cache freshness check: {second_check_time:.3f}s")

    def test_memory_optimization_performance(self):
        """测试内存优化性能"""
        # 创建一些内存占用
        large_data = [list(range(10000)) for _ in range(100)]

        start_time = time.time()
        _optimize_memory_usage_enhanced()
        optimization_time = time.time() - start_time

        self.assertLess(optimization_time, 2.0)  # 内存优化应小于2秒

        print(f"Memory optimization time: {optimization_time:.3f}s")

        # 清理测试数据
        del large_data

    def test_performance_report_generation(self):
        """测试性能报告生成"""
        # 添加一些测试数据
        for i in range(10):
            _update_cache_stats(0.1 + i * 0.05, 50 + i * 5, f"v2.0_test_{i}")

        start_time = time.time()
        report = _get_cache_performance_report()
        report_time = time.time() - start_time

        self.assertIn("avg_load_time", report)
        self.assertIn("hit_rate", report)
        self.assertEqual(report["total_loads"], 10)
        self.assertLess(report_time, 0.1)  # 报告生成应小于0.1秒

        print(f"Performance report generation time: {report_time:.3f}s")
        print(f"Average load time: {report['avg_load_time']:.3f}s")
        print(f"Hit rate: {report['hit_rate']:.2%}")


class TestUnifiedDataMappingEngineIntegration(unittest.TestCase):
    """统一数据映射引擎集成测试"""

    def setUp(self):
        """集成测试准备"""
        self.temp_dir = tempfile.mkdtemp()

        # 创建字段映射配置文件
        self.field_mapping_config = {
            "version": "v2.0",
            "common_fields": {
                "rule_id": {"type": "string", "required": True},
                "rule_name": {"type": "string", "required": True},
                "level1": {"type": "string", "required": True},
                "level2": {"type": "string", "required": True},
                "level3": {"type": "string", "required": True},
                "error_reason": {"type": "string", "required": True},
            },
            "field_mapping": {"error_level_1": "level1", "error_level_2": "level2", "error_level_3": "level3"},
        }

        self.config_file = os.path.join(self.temp_dir, "field_mapping.json")
        with open(self.config_file, "w", encoding="utf-8") as f:
            json.dump(self.field_mapping_config, f, ensure_ascii=False)

    def tearDown(self):
        """集成测试清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_unified_mapping_engine_integration(self):
        """测试统一数据映射引擎集成 - 简化版本"""
        from services.rule_loader import _convert_rule_detail_to_cache_format
        from models.database import RuleDetail

        # 创建模拟的RuleDetail对象，使用实际数据库模型的字段
        mock_rule_detail = Mock(spec=RuleDetail)
        mock_rule_detail.rule_detail_id = "test_detail_1"
        mock_rule_detail.rule_key = "test_rule"
        mock_rule_detail.rule_name = "测试规则"
        mock_rule_detail.level1 = "错误"
        mock_rule_detail.level2 = "数据"
        mock_rule_detail.level3 = "缺失"
        mock_rule_detail.error_reason = "必填字段为空"
        mock_rule_detail.to_dict.return_value = {
            "rule_detail_id": "test_detail_1",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "level1": "错误",
            "level2": "数据",
            "level3": "缺失",
            "error_reason": "必填字段为空",
        }

        # 模拟UnifiedDataMappingEngine的行为
        with patch("services.rule_loader.UnifiedDataMappingEngine") as mock_engine:
            mock_instance = Mock()
            mock_instance.normalize_field_names.return_value = {
                "rule_detail_id": "test_detail_1",
                "rule_key": "test_rule",
                "rule_name": "测试规则",
                "level1": "错误",
                "level2": "数据",
                "level3": "缺失",
                "error_reason": "必填字段为空",
            }
            mock_instance.validate_data.return_value = {"is_valid": True}
            mock_instance.convert_to_structured_format.return_value = {
                "rule_id": "test_detail_1",
                "rule_key": "test_rule",
                "rule_name": "测试规则",
                "level1": "错误",
                "level2": "数据",
                "level3": "缺失",
                "error_reason": "必填字段为空",
            }
            mock_engine.return_value = mock_instance

            # 测试转换功能
            result = _convert_rule_detail_to_cache_format(mock_rule_detail)
            
            self.assertIsNotNone(result)
            self.assertIn("rule_id", result)
            self.assertEqual(result["rule_id"], "test_detail_1")

    def test_field_mapping_validation_integration(self):
        """测试字段映射验证集成"""
        from services.rule_loader import _validate_field_mapping_configuration

        with patch("services.rule_loader.UnifiedDataMappingEngine") as mock_engine:
            mock_instance = Mock()
            mock_instance.get_engine_info.return_value = {
                "field_manager_info": {
                    "config_loaded": True,
                    "config_version": "v2.0",
                    "field_definitions": {
                        "common_fields": {
                            "rule_id": {"type": "string", "required": True},
                            "rule_name": {"type": "string", "required": True},
                            "level1": {"type": "string", "required": True},
                            "level2": {"type": "string", "required": True},
                            "level3": {"type": "string", "required": True},
                            "error_reason": {"type": "string", "required": True},
                        }
                    }
                }
            }
            mock_engine.return_value = mock_instance

            result = _validate_field_mapping_configuration()
            self.assertTrue(result)


class TestRuleDataSyncServiceIntegration(unittest.TestCase):
    """规则数据同步服务集成测试"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_file = os.path.join(self.temp_dir, "test_sync_cache.json.gz")

        # 创建测试数据
        self.test_sync_data = {
            "version": "v2.0",
            "metadata": {"export_timestamp": "2025-07-27T10:00:00Z", "total_templates": 2, "total_details": 4},
            "templates": [
                {
                    "rule_template_id": "template_1",
                    "rule_key": "test_rule_1",
                    "rule_name": "测试规则1",
                    "status": "active",
                },
                {
                    "rule_template_id": "template_2",
                    "rule_key": "test_rule_2",
                    "rule_name": "测试规则2",
                    "status": "active",
                },
            ],
            "details": [
                {
                    "rule_detail_id": "detail_1",
                    "rule_key": "test_rule_1",
                    "level1": "错误",
                    "level2": "数据",
                    "level3": "缺失",
                    "error_reason": "必填字段为空",
                },
                {
                    "rule_detail_id": "detail_2",
                    "rule_key": "test_rule_1",
                    "level1": "警告",
                    "level2": "格式",
                    "level3": "异常",
                    "error_reason": "日期格式不正确",
                },
                {
                    "rule_detail_id": "detail_3",
                    "rule_key": "test_rule_2",
                    "level1": "错误",
                    "level2": "逻辑",
                    "level3": "冲突",
                    "error_reason": "业务逻辑冲突",
                },
                {
                    "rule_detail_id": "detail_4",
                    "rule_key": "test_rule_2",
                    "level1": "信息",
                    "level2": "提示",
                    "level3": "建议",
                    "error_reason": "建议优化配置",
                },
            ],
        }

        # 创建缓存文件
        with gzip.open(self.cache_file, "wt", encoding="utf-8") as f:
            json.dump(self.test_sync_data, f, ensure_ascii=False)

    def tearDown(self):
        """测试后清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        RULE_CACHE.clear()

    def test_sync_service_load_integration(self):
        """测试同步服务加载集成"""
        with patch("services.rule_loader.LOCAL_RULES_PATH", self.cache_file):
            # 模拟RuleDataSyncService
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                mock_stats = Mock()
                mock_stats.total_templates = 2
                mock_stats.total_details = 4
                mock_stats.sync_duration = 0.5
                mock_stats.cache_size_mb = 1.5

                mock_instance = Mock()
                mock_instance.load_from_cache.return_value = mock_stats
                mock_sync_service.return_value = mock_instance

                # 执行加载测试
                result = asyncio.run(load_rules_from_file())

                self.assertTrue(result)
                mock_sync_service.assert_called_once_with(cache_file_path=self.cache_file)
                mock_instance.load_from_cache.assert_called_once()

    @patch("services.rule_loader.get_session_factory")
    def test_sync_service_database_integration(self, mock_session_factory):
        """测试同步服务数据库集成"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session_factory.return_value.return_value.__enter__ = Mock(return_value=mock_session)
        mock_session_factory.return_value.return_value.__exit__ = Mock(return_value=None)

        # 模拟RuleDataSyncService
        with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
            mock_stats = Mock()
            mock_stats.total_templates = 2
            mock_stats.total_details = 4
            mock_stats.sync_duration = 1.0
            mock_stats.cache_size_mb = 2.0

            mock_instance = Mock()
            mock_instance.sync_from_database.return_value = mock_stats
            mock_sync_service.return_value = mock_instance

            # 执行数据库加载测试
            load_rules_into_cache()

            mock_sync_service.assert_called_once()
            mock_instance.sync_from_database.assert_called_once()


class TestErrorRecoveryIntegration(unittest.TestCase):
    """错误恢复集成测试"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        RULE_CACHE.clear()

    def tearDown(self):
        """测试后清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        RULE_CACHE.clear()

    def test_file_not_found_recovery(self):
        """测试文件不存在的恢复机制"""
        non_existent_file = os.path.join(self.temp_dir, "non_existent.json.gz")

        with patch("services.rule_loader.LOCAL_RULES_PATH", non_existent_file):
            result = asyncio.run(load_rules_from_file())

            # 应该返回False，表示加载失败
            self.assertFalse(result)

    def test_corrupted_file_recovery(self):
        """测试损坏文件的恢复机制"""
        corrupted_file = os.path.join(self.temp_dir, "corrupted.json.gz")

        # 创建损坏的文件
        with open(corrupted_file, "wb") as f:
            f.write(b"corrupted data that is not gzip")

        with patch("services.rule_loader.LOCAL_RULES_PATH", corrupted_file):
            result = asyncio.run(load_rules_from_file())

            # 应该返回False或触发降级处理
            self.assertIsNotNone(result)

    def test_sync_service_failure_recovery(self):
        """测试同步服务失败的处理机制"""
        test_file = os.path.join(self.temp_dir, "test_cache.json.gz")

        # 创建有效的测试文件
        test_data = {"version": "v2.0", "templates": [], "details": []}
        with gzip.open(test_file, "wt", encoding="utf-8") as f:
            json.dump(test_data, f)

        with patch("services.rule_loader.LOCAL_RULES_PATH", test_file):
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                # 模拟同步服务失败
                mock_sync_service.side_effect = Exception("Sync service failed")

                result = asyncio.run(load_rules_from_file())

                # 应该返回False，表示加载失败（不再支持降级）
                self.assertFalse(result)


class TestMemoryPerformanceIntegration(unittest.TestCase):
    """内存性能集成测试"""

    def setUp(self):
        """性能测试准备"""
        self.temp_dir = tempfile.mkdtemp()
        RULE_CACHE.clear()
        _clear_cache_stats()

    def tearDown(self):
        """性能测试清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        RULE_CACHE.clear()
        _clear_cache_stats()

    def test_memory_optimization_integration(self):
        """测试内存优化集成"""
        from services.rule_loader import _optimize_memory_usage_enhanced

        # 创建一些内存占用
        large_objects = [list(range(1000)) for _ in range(50)]

        # 执行内存优化
        start_time = time.time()
        _optimize_memory_usage_enhanced()
        optimization_time = time.time() - start_time

        # 验证优化时间合理
        self.assertLess(optimization_time, 5.0)

        # 清理测试对象
        del large_objects

    def test_cache_performance_monitoring_integration(self):
        """测试缓存性能监控集成"""
        from services.rule_loader import _get_cache_performance_report, _update_cache_stats

        # 模拟多次加载统计
        for i in range(5):
            _update_cache_stats(0.1 + i * 0.02, 10.0 + i * 2.0, f"v2.0_test_{i}")

        # 获取性能报告
        report = _get_cache_performance_report()

        self.assertIn("avg_load_time", report)
        self.assertIn("avg_memory_usage", report)
        self.assertIn("hit_rate", report)
        self.assertEqual(report["total_loads"], 5)
        self.assertGreater(report["avg_load_time"], 0)

    @patch("services.rule_loader.performance_monitor")
    def test_performance_monitor_integration(self, mock_monitor):
        """测试性能监控器集成"""
        mock_monitor.record_rule_loading = Mock()

        # 模拟加载过程
        with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
            mock_stats = Mock()
            mock_stats.total_templates = 1
            mock_stats.total_details = 2
            mock_stats.sync_duration = 0.5
            mock_stats.cache_size_mb = 1.0

            mock_instance = Mock()
            mock_instance.sync_from_database.return_value = mock_stats
            mock_sync_service.return_value = mock_instance

            with patch("services.rule_loader.get_session_factory") as mock_session_factory:
                mock_session = Mock()
                mock_session_factory.return_value.return_value.__enter__ = Mock(return_value=mock_session)
                mock_session_factory.return_value.return_value.__exit__ = Mock(return_value=None)

                load_rules_into_cache()

                # 验证性能监控被调用
                mock_monitor.record_rule_loading.assert_called_once()


if __name__ == "__main__":
    # 运行所有测试
    unittest.main(verbosity=2)
