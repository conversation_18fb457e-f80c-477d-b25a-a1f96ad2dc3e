#!/usr/bin/env python3
"""
测试从规则类中提取字段信息的功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer
from tools.field_mapping_manager import FieldMappingManager
from models.database import get_session_factory
from loguru import logger


def test_single_rule_extraction():
    """测试单个规则的字段提取"""
    logger.info("=== 测试单个规则字段提取 ===")
    
    try:
        # 测试 drug_limit_max_pay_days 规则
        from rules.base_rules.drug_limit_max_pay_days import DrugLimitMaxPayDaysRule
        import inspect
        
        # 获取__init__方法的签名
        init_signature = inspect.signature(DrugLimitMaxPayDaysRule.__init__)
        parameters = list(init_signature.parameters.keys())
        
        logger.info(f"规则类 {DrugLimitMaxPayDaysRule.rule_key} 的参数:")
        for param_name in parameters:
            if param_name not in ['self', 'rule_id']:
                param = init_signature.parameters[param_name]
                is_optional = (param.default != inspect.Parameter.empty or
                             (param.annotation != inspect.Parameter.empty and 
                              'None' in str(param.annotation)))
                logger.info(f"  - {param_name}: {'可选' if is_optional else '必填'}")
        
        return True
        
    except Exception as e:
        logger.error(f"单个规则字段提取测试失败: {e}")
        return False


def test_batch_rule_extraction():
    """测试批量规则字段提取"""
    logger.info("=== 测试批量规则字段提取 ===")
    
    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)
        
        # 提取所有规则的字段映射
        extracted_mappings = metadata_initializer._extract_rule_mappings_from_classes()
        
        logger.info(f"成功提取了 {len(extracted_mappings)} 个规则的字段映射:")
        
        for rule_key, mapping in extracted_mappings.items():
            required_fields = mapping.get('required_fields', [])
            optional_fields = mapping.get('optional_fields', [])
            
            logger.info(f"\n规则: {rule_key}")
            logger.info(f"  名称: {mapping.get('name', rule_key)}")
            logger.info(f"  必填字段 ({len(required_fields)}): {', '.join(required_fields)}")
            logger.info(f"  可选字段 ({len(optional_fields)}): {', '.join(optional_fields)}")
        
        return len(extracted_mappings) > 0
        
    except Exception as e:
        logger.error(f"批量规则字段提取测试失败: {e}")
        return False


def compare_with_config():
    """对比提取的字段与配置文件中的字段"""
    logger.info("=== 对比提取字段与配置文件 ===")
    
    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)
        
        # 从规则类提取
        extracted_mappings = metadata_initializer._extract_rule_mappings_from_classes()
        
        # 从配置文件获取
        config_mappings = field_mapping_manager.config.get("rule_type_mappings", {})
        
        logger.info(f"提取的规则数量: {len(extracted_mappings)}")
        logger.info(f"配置的规则数量: {len(config_mappings)}")
        
        # 对比差异
        extracted_keys = set(extracted_mappings.keys())
        config_keys = set(config_mappings.keys())
        
        only_in_extracted = extracted_keys - config_keys
        only_in_config = config_keys - extracted_keys
        common_keys = extracted_keys & config_keys
        
        if only_in_extracted:
            logger.warning(f"只在提取中存在的规则: {only_in_extracted}")
        
        if only_in_config:
            logger.warning(f"只在配置中存在的规则: {only_in_config}")
        
        logger.info(f"共同的规则数量: {len(common_keys)}")
        
        # 对比共同规则的字段差异
        field_differences = []
        for rule_key in common_keys:
            extracted = extracted_mappings[rule_key]
            config = config_mappings[rule_key]
            
            extracted_required = set(extracted.get('required_fields', []))
            config_required = set(config.get('required_fields', []))
            
            if extracted_required != config_required:
                field_differences.append({
                    'rule_key': rule_key,
                    'extracted_required': extracted_required,
                    'config_required': config_required,
                    'missing_in_extracted': config_required - extracted_required,
                    'extra_in_extracted': extracted_required - config_required
                })
        
        if field_differences:
            logger.warning(f"发现 {len(field_differences)} 个规则的字段定义存在差异:")
            for diff in field_differences:
                logger.warning(f"  规则 {diff['rule_key']}:")
                if diff['missing_in_extracted']:
                    logger.warning(f"    提取中缺少: {diff['missing_in_extracted']}")
                if diff['extra_in_extracted']:
                    logger.warning(f"    提取中多余: {diff['extra_in_extracted']}")
        else:
            logger.success("所有共同规则的字段定义都一致")
        
        return True
        
    except Exception as e:
        logger.error(f"字段对比测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始测试规则字段提取功能")
    
    tests = [
        ("单个规则字段提取", test_single_rule_extraction),
        ("批量规则字段提取", test_batch_rule_extraction),
        ("字段对比测试", compare_with_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试项目: {name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.success(f"✅ {name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {name} - 失败")
        except Exception as e:
            logger.error(f"❌ {name} - 异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {passed}/{total} 项通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.success("🎉 所有测试都通过！")
        return 0
    else:
        logger.error("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
