"""
日志系统集成测试

测试日志系统与环境配置的集成功能，确保日志系统在不同环境下正常工作。

作者: 系统架构师
创建时间: 2025-07-01
版本: 1.0.0
"""

import os
import shutil
import tempfile
import unittest
from unittest.mock import patch

from config.environment_log_config import LogConfigFactory
from core.logging.logging_system import LoggingSystem, reinitialize_logging_system


class TestLoggingSystemIntegration(unittest.TestCase):
    """日志系统集成测试"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录用于测试日志文件
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)

    def test_logging_system_with_environment_config(self):
        """测试日志系统使用环境配置"""
        # 创建使用环境配置的日志系统
        logging_system = LoggingSystem(use_environment_config=True)

        # 验证环境配置被正确加载
        self.assertIsNotNone(logging_system.env_config)
        self.assertIsNotNone(logging_system.config)

        # 验证配置包含环境特定信息
        self.assertIn("environment", logging_system.config)
        self.assertIn("sensitive_filter", logging_system.config)

    def test_logging_system_with_traditional_config(self):
        """测试日志系统使用传统配置"""
        # 创建使用传统配置的日志系统
        custom_config = {
            "level": "INFO",
            "format": "{time} | {level} | {message}",
            "path": os.path.join(self.temp_dir, "test1.log"),
            "rotation": "1 MB",
            "retention": "1 day",
            "compression": "zip",
            "enqueue": True,
            "stdout_sink": {"enabled": True, "level": "INFO"},
        }

        logging_system = LoggingSystem(config=custom_config, use_environment_config=False)

        # 验证传统配置被正确加载
        self.assertIsNone(logging_system.env_config)
        self.assertEqual(logging_system.config, custom_config)

    @patch("config.environment_log_config.settings")
    def test_different_environments_create_different_systems(self, mock_settings):
        """测试不同环境创建不同的日志系统"""
        # 测试开发环境
        mock_settings.RUN_MODE = "DEV"
        mock_settings.LOG_DEV_LEVEL = "DEBUG"
        mock_settings.LOG_DEV_STDOUT_ENABLED = True
        mock_settings.LOG_DEV_ROTATION = "5 MB"
        mock_settings.LOG_DEV_RETENTION = "3 days"
        mock_settings.LOG_FILTER_SENSITIVE_DATA = True
        dev_system = LoggingSystem(use_environment_config=True)
        dev_config = dev_system.config

        # 测试生产环境
        mock_settings.RUN_MODE = "PROD"
        mock_settings.LOG_PROD_LEVEL = "WARNING"
        mock_settings.LOG_PROD_STDOUT_ENABLED = False
        mock_settings.LOG_PROD_ROTATION = "50 MB"
        mock_settings.LOG_PROD_RETENTION = "30 days"
        mock_settings.LOG_PROD_COMPRESSION = "gz"
        prod_system = LoggingSystem(use_environment_config=True)
        prod_config = prod_system.config

        # 验证配置不同
        self.assertNotEqual(dev_config["level"], prod_config["level"])
        self.assertNotEqual(dev_config["stdout_sink"]["enabled"], prod_config["stdout_sink"]["enabled"])

    def test_log_filter_functionality(self):
        """测试日志过滤功能"""
        # 直接创建测试环境的日志系统，修改settings
        from config.settings import settings

        original_run_mode = settings.RUN_MODE
        settings.RUN_MODE = "TEST"

        try:
            logging_system = LoggingSystem(use_environment_config=True)

            # 获取日志过滤器
            log_filter = logging_system._create_log_filter()
            self.assertIsNotNone(log_filter)

            # 调试信息
            print(f"env_config: {logging_system.env_config}")
            print(f"has sensitive_filter: {hasattr(logging_system.env_config, 'sensitive_filter')}")
            if hasattr(logging_system.env_config, "sensitive_filter"):
                print(f"patterns: {logging_system.env_config.sensitive_filter.patterns}")

            # 创建模拟日志记录
            mock_record = {"message": "password=secret123", "extra": {}}
            print(f"before filter: {mock_record['message']}")

            # 应用过滤器
            result = log_filter(mock_record)
            print(f"after filter: {mock_record['message']}")

            # 验证过滤结果
            self.assertTrue(result)  # 过滤器应该返回True允许记录
            self.assertIn("***FILTERED***", mock_record["message"])
            self.assertNotIn("secret123", mock_record["message"])
        finally:
            # 恢复原始设置
            settings.RUN_MODE = original_run_mode

    def test_reinitialize_logging_system(self):
        """测试重新初始化日志系统"""
        # 初始化日志系统
        original_system = LoggingSystem(use_environment_config=True)
        original_config = original_system.config  # noqa: F841

        # 重新初始化
        reinitialize_logging_system(use_environment_config=True)

        # 验证系统被重新初始化（这里主要验证函数不会抛出异常）
        # 在实际应用中，可以通过检查全局logger实例来验证

    def test_file_sink_configuration(self):
        """测试文件日志处理器配置"""
        # 创建自定义配置
        test_log_path = os.path.join(self.temp_dir, "test_{time}.log")  # noqa: F841

        with patch("config.environment_log_config.settings") as mock_settings:
            mock_settings.RUN_MODE = "DEV"
            mock_settings.LOG_DEV_LEVEL = "DEBUG"
            mock_settings.LOG_DEV_STDOUT_ENABLED = True
            mock_settings.LOG_DEV_ROTATION = "1 MB"
            mock_settings.LOG_DEV_RETENTION = "1 day"
            mock_settings.LOG_FILTER_SENSITIVE_DATA = True

            logging_system = LoggingSystem(use_environment_config=True)

            # 验证文件配置
            file_config = logging_system.config.get("file_sink", {})
            self.assertIn("dev", file_config.get("path", ""))
            self.assertEqual(file_config.get("rotation"), "1 MB")
            self.assertEqual(file_config.get("retention"), "1 day")

    def test_stdout_sink_configuration(self):
        """测试控制台日志处理器配置"""
        with patch("config.environment_log_config.settings") as mock_settings:
            # 测试开发环境（启用控制台输出）
            mock_settings.RUN_MODE = "DEV"
            mock_settings.LOG_DEV_LEVEL = "DEBUG"
            mock_settings.LOG_DEV_STDOUT_ENABLED = True
            mock_settings.LOG_DEV_ROTATION = "5 MB"
            mock_settings.LOG_DEV_RETENTION = "3 days"
            mock_settings.LOG_FILTER_SENSITIVE_DATA = True

            dev_system = LoggingSystem(use_environment_config=True)
            dev_stdout_config = dev_system.config.get("stdout_sink", {})
            self.assertTrue(dev_stdout_config.get("enabled", False))

            # 测试生产环境（禁用控制台输出）
            mock_settings.RUN_MODE = "PROD"
            mock_settings.LOG_PROD_LEVEL = "WARNING"
            mock_settings.LOG_PROD_STDOUT_ENABLED = False
            mock_settings.LOG_PROD_ROTATION = "50 MB"
            mock_settings.LOG_PROD_RETENTION = "30 days"
            mock_settings.LOG_PROD_COMPRESSION = "gz"

            prod_system = LoggingSystem(use_environment_config=True)
            prod_stdout_config = prod_system.config.get("stdout_sink", {})
            self.assertFalse(prod_stdout_config.get("enabled", True))

    def test_sensitive_filter_integration(self):
        """测试敏感信息过滤器集成"""
        with patch("config.environment_log_config.settings") as mock_settings:
            mock_settings.RUN_MODE = "PROD"
            mock_settings.LOG_PROD_LEVEL = "WARNING"
            mock_settings.LOG_PROD_STDOUT_ENABLED = False
            mock_settings.LOG_PROD_ROTATION = "50 MB"
            mock_settings.LOG_PROD_RETENTION = "30 days"
            mock_settings.LOG_PROD_COMPRESSION = "gz"
            mock_settings.LOG_FILTER_SENSITIVE_DATA = True

            logging_system = LoggingSystem(use_environment_config=True)

            # 验证敏感信息过滤器存在
            self.assertIsNotNone(logging_system.env_config)
            self.assertTrue(hasattr(logging_system.env_config, "sensitive_filter"))

            # 验证过滤器配置正确
            sensitive_filter = logging_system.env_config.sensitive_filter
            self.assertEqual(sensitive_filter.environment, "PROD")

    def test_environment_context_integration(self):
        """测试环境上下文集成"""
        with patch("config.environment_log_config.settings") as mock_settings:
            mock_settings.RUN_MODE = "TEST"
            mock_settings.MODE = "master"
            mock_settings.LOG_TEST_LEVEL = "INFO"
            mock_settings.LOG_TEST_STDOUT_ENABLED = False
            mock_settings.LOG_TEST_ROTATION = "10 MB"
            mock_settings.LOG_TEST_RETENTION = "7 days"
            mock_settings.LOG_FILTER_SENSITIVE_DATA = True

            logging_system = LoggingSystem(use_environment_config=True)

            # 验证环境上下文
            context = logging_system.config.get("context", {})
            self.assertEqual(context.get("environment"), "TEST")
            self.assertEqual(context.get("mode"), "master")
            self.assertEqual(context.get("run_mode"), "TEST")

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 使用旧的配置方式
        from config.logging_config import LOGGING_CONFIG

        old_system = LoggingSystem(config=LOGGING_CONFIG, use_environment_config=False)

        # 验证旧配置仍然工作
        self.assertIsNone(old_system.env_config)
        self.assertIsNotNone(old_system.config)

        # 验证基本配置项存在
        self.assertIn("level", old_system.config)
        self.assertIn("format", old_system.config)

    def test_error_handling_in_filter(self):
        """测试过滤器中的错误处理"""
        with patch("config.environment_log_config.settings") as mock_settings:
            mock_settings.RUN_MODE = "TEST"
            mock_settings.LOG_TEST_LEVEL = "INFO"
            mock_settings.LOG_TEST_STDOUT_ENABLED = False
            mock_settings.LOG_TEST_ROTATION = "10 MB"
            mock_settings.LOG_TEST_RETENTION = "7 days"
            mock_settings.LOG_FILTER_SENSITIVE_DATA = True

            logging_system = LoggingSystem(use_environment_config=True)
            log_filter = logging_system._create_log_filter()

            # 测试异常情况
            mock_record = {"extra": {}}  # 缺少message字段

            # 过滤器应该能够处理异常情况而不崩溃
            try:
                result = log_filter(mock_record)
                self.assertTrue(result)
            except Exception as e:
                self.fail(f"日志过滤器应该能够处理异常情况: {e}")


class TestPerformanceAndStability(unittest.TestCase):
    """性能和稳定性测试"""

    def test_config_creation_performance(self):
        """测试配置创建性能"""
        import time

        start_time = time.time()

        # 创建多个配置实例
        for _ in range(100):
            config = LogConfigFactory.create_config("DEV")
            built_config = config.build_config()  # noqa: F841

        end_time = time.time()
        elapsed_time = end_time - start_time

        # 验证性能（100次创建应该在1秒内完成）
        self.assertLess(elapsed_time, 1.0, "配置创建性能不符合要求")

    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        # 创建多个日志系统实例
        systems = []

        for _ in range(10):
            system = LoggingSystem(use_environment_config=True)
            systems.append(system)

        # 验证所有系统都正常创建
        self.assertEqual(len(systems), 10)

        # 清理
        del systems

    def test_concurrent_access(self):
        """测试并发访问"""
        import threading

        results = []
        errors = []

        def create_logging_system():
            try:
                system = LoggingSystem(use_environment_config=True)
                results.append(system)
            except Exception as e:
                errors.append(e)

        # 创建多个线程并发创建日志系统
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=create_logging_system)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0, f"并发访问出现错误: {errors}")
        self.assertEqual(len(results), 5, "并发创建的日志系统数量不正确")


if __name__ == "__main__":
    unittest.main()
