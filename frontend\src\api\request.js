import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT) || 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加API密钥
    const apiKey = import.meta.env.VITE_API_KEY
    if (apiKey) {
      config.headers['X-API-KEY'] = apiKey
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 打印请求日志（开发环境）
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.log('🚀 Request:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data
      })
    }

    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 打印响应日志（开发环境）
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.log('✅ Response:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      })
    }

    // 对于blob类型的响应，返回完整的response对象以便访问headers
    if (response.config.responseType === 'blob') {
      return response
    }

    return response.data
  },
  (error) => {
    console.error('Response Error:', error)

    // 处理不同类型的错误
    let message = '请求失败'

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response

      switch (status) {
        case 400:
          message = data?.detail || '请求参数错误'
          break
        case 401:
          message = '未授权，请检查API密钥'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 422:
          message = data?.detail || '请求数据验证失败'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        default:
          message = data?.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      message = error.message || '未知错误'
    }

    // 显示错误消息
    ElMessage.error(message)

    return Promise.reject(error)
  }
)

/**
 * 通用请求方法
 * @param {Object} config - 请求配置
 * @returns {Promise} 请求Promise
 */
export function apiRequest(config) {
  return request(config)
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function get(url, params = {}, config = {}) {
  return request({
    method: 'get',
    url,
    params,
    ...config
  })
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function post(url, data = {}, config = {}) {
  return request({
    method: 'post',
    url,
    data,
    ...config
  })
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function put(url, data = {}, config = {}) {
  return request({
    method: 'put',
    url,
    data,
    ...config
  })
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} config - 额外配置
 * @returns {Promise} 请求Promise
 */
export function del(url, config = {}) {
  return request({
    method: 'delete',
    url,
    ...config
  })
}

/**
 * 文件下载
 * @param {string} url - 下载URL
 * @param {Object} params - 请求参数
 * @param {string} filename - 文件名
 * @returns {Promise} 请求Promise
 */
export function downloadFile(url, params = {}, filename = '') {
  return request({
    method: 'get',
    url,
    params,
    responseType: 'blob'
  }).then(response => {
    // 创建下载链接
    const blob = new Blob([response])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    return response
  })
}

export default request
