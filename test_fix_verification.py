#!/usr/bin/env python3
"""
验证SQLite自增字段修复的测试脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def run_specific_test():
    """运行特定的失败测试"""
    print("🔧 验证SQLite自增字段修复...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = str(Path.cwd())
    
    # 运行特定的失败测试
    test_command = [
        sys.executable, "-m", "pytest",
        "tests/integration/test_rule_details_api_v2.py::TestRuleDetailsAPIv2::test_field_mapping_consistency",
        "-v",  # 详细输出
        "-s",  # 不捕获输出，显示print语句
        "--tb=short",  # 简短的traceback
        "-p", "no:warnings",  # 禁用警告
    ]
    
    print(f"📋 运行测试命令: {' '.join(test_command)}")
    
    try:
        result = subprocess.run(test_command, env=env, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ 测试通过！SQLite自增字段问题已修复")
        else:
            print("❌ 测试仍然失败，需要进一步调试")
            
        return result.returncode
        
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return 1


def run_mysql_test():
    """运行MySQL测试进行对比"""
    print("\n🔧 运行MySQL测试进行对比...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = str(Path.cwd())
    
    # 运行MySQL测试
    mysql_test_command = [
        sys.executable, "-m", "pytest",
        "tests/integration/test_rule_details_mysql.py::TestRuleDetailsAPIMysql::test_field_mapping_consistency_mysql",
        "-v",
        "-s",
        "--tb=short",
        "-p", "no:warnings",
        "--confcutdir=tests/integration",
    ]
    
    print(f"📋 运行MySQL测试命令: {' '.join(mysql_test_command)}")
    
    try:
        result = subprocess.run(mysql_test_command, env=env, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ MySQL测试通过！")
        else:
            print("❌ MySQL测试失败")
            
        return result.returncode
        
    except Exception as e:
        print(f"❌ 运行MySQL测试时出错: {e}")
        return 1


def main():
    """主函数"""
    print("🚀 开始验证修复效果...")
    
    # 1. 先运行原始的SQLite测试
    sqlite_result = run_specific_test()
    
    # 2. 运行MySQL测试进行对比
    mysql_result = run_mysql_test()
    
    # 3. 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print(f"SQLite测试: {'✅ 通过' if sqlite_result == 0 else '❌ 失败'}")
    print(f"MySQL测试: {'✅ 通过' if mysql_result == 0 else '❌ 失败'}")
    
    if sqlite_result == 0 and mysql_result == 0:
        print("🎉 所有测试都通过！修复成功")
        return 0
    elif sqlite_result == 0:
        print("✅ SQLite问题已修复，但MySQL测试需要检查")
        return 0
    else:
        print("❌ 仍有问题需要解决")
        return 1


if __name__ == "__main__":
    sys.exit(main())
