#!/usr/bin/env python3
"""
标准化数据库模型单元测试
测试新的标准化模型的功能
"""

from models.database import (
    FieldTypeEnum,
    RuleDetail,
    RuleDetailStatusEnum,
    RuleFieldMetadata,
    RuleTemplate,
    RuleTemplateStatusEnum,
)


class TestRuleTemplate:
    """RuleTemplate 模型单元测试"""

    def test_rule_template_creation(self):
        """测试 RuleTemplate 创建"""
        template = RuleTemplate(
            rule_key="unit_test_rule",
            rule_type="unit_test",
            name="单元测试规则模板",
            description="用于单元测试的规则模板",
            status=RuleTemplateStatusEnum.NEW,
        )

        assert template.rule_key == "unit_test_rule"
        assert template.rule_type == "unit_test"
        assert template.name == "单元测试规则模板"
        assert template.status == RuleTemplateStatusEnum.NEW

    def test_rule_template_to_dict(self):
        """测试 RuleTemplate to_dict 方法"""
        template = RuleTemplate(
            rule_key="unit_test_rule",
            rule_type="unit_test",
            name="单元测试规则模板",
            status=RuleTemplateStatusEnum.READY,
        )

        result = template.to_dict()

        assert isinstance(result, dict)
        assert result["rule_key"] == "unit_test_rule"
        assert result["rule_type"] == "unit_test"
        assert result["name"] == "单元测试规则模板"
        assert result["status"] == "READY"

    def test_rule_template_status_enum(self):
        """测试 RuleTemplateStatusEnum"""
        assert RuleTemplateStatusEnum.NEW.value == "NEW"
        assert RuleTemplateStatusEnum.CHANGED.value == "CHANGED"
        assert RuleTemplateStatusEnum.READY.value == "READY"
        assert RuleTemplateStatusEnum.DEPRECATED.value == "DEPRECATED"


class TestRuleDetail:
    """RuleDetail 模型单元测试"""

    def test_rule_detail_creation(self):
        """测试 RuleDetail 创建"""
        detail = RuleDetail(
            rule_id="unit_test_001",
            rule_key="unit_test_rule",
            rule_name="单元测试规则明细",
            level1="单元测试一级",
            level2="单元测试二级",
            level3="单元测试三级",
            error_reason="单元测试错误原因",
            degree="轻微",
            reference="单元测试参考资料",
            detail_position="单元测试位置",
            prompted_fields1="unit_test_field",
            type="单元测试类型",
            pos="单元测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2025-01-01",
            end_date="2025-12-31",
        )

        # 验证标准字段名
        assert detail.level1 == "单元测试一级"
        assert detail.level2 == "单元测试二级"
        assert detail.level3 == "单元测试三级"
        assert detail.degree == "轻微"
        assert detail.reference == "单元测试参考资料"
        assert detail.detail_position == "单元测试位置"

    def test_rule_detail_to_dict(self):
        """测试 RuleDetail to_dict 方法"""
        detail = RuleDetail(
            rule_id="unit_test_001",
            rule_key="unit_test_rule",
            rule_name="单元测试规则明细",
            level1="单元测试一级",
            level2="单元测试二级",
            level3="单元测试三级",
            error_reason="单元测试错误原因",
            degree="轻微",
            reference="单元测试参考资料",
            detail_position="单元测试位置",
            prompted_fields1="unit_test_field",
            type="单元测试类型",
            pos="单元测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2025-01-01",
            end_date="2025-12-31",
        )

        result = detail.to_dict()

        assert isinstance(result, dict)
        # 验证标准字段名在序列化结果中
        assert result["level1"] == "单元测试一级"
        assert result["level2"] == "单元测试二级"
        assert result["level3"] == "单元测试三级"
        assert result["degree"] == "轻微"
        assert result["reference"] == "单元测试参考资料"
        assert result["detail_position"] == "单元测试位置"

    def test_rule_detail_status_enum(self):
        """测试 RuleDetailStatusEnum"""
        assert RuleDetailStatusEnum.ACTIVE.value == "ACTIVE"
        assert RuleDetailStatusEnum.INACTIVE.value == "INACTIVE"
        assert RuleDetailStatusEnum.DEPRECATED.value == "DEPRECATED"


class TestRuleFieldMetadata:
    """RuleFieldMetadata 模型单元测试"""

    def test_rule_field_metadata_creation(self):
        """测试 RuleFieldMetadata 创建"""
        metadata = RuleFieldMetadata(
            rule_key="unit_test_rule",
            field_name="level1",
            field_type=FieldTypeEnum.STRING,
            is_required=True,
            is_fixed_field=True,
            display_name="一级错误类型",
            description="规则的一级错误类型分类",
        )

        assert metadata.rule_key == "unit_test_rule"
        assert metadata.field_name == "level1"
        assert metadata.field_type == FieldTypeEnum.STRING
        assert metadata.is_required is True
        assert metadata.is_fixed_field is True

    def test_rule_field_metadata_to_dict(self):
        """测试 RuleFieldMetadata to_dict 方法"""
        metadata = RuleFieldMetadata(
            rule_key="unit_test_rule",
            field_name="level1",
            field_type=FieldTypeEnum.STRING,
            is_required=True,
            display_name="一级错误类型",
        )

        result = metadata.to_dict()

        assert isinstance(result, dict)
        assert result["rule_key"] == "unit_test_rule"
        assert result["field_name"] == "level1"
        assert result["field_type"] == "string"
        assert result["is_required"] is True

    def test_field_type_enum(self):
        """测试 FieldTypeEnum"""
        assert FieldTypeEnum.STRING.value == "string"
        assert FieldTypeEnum.INTEGER.value == "integer"
        assert FieldTypeEnum.ARRAY.value == "array"
        assert FieldTypeEnum.BOOLEAN.value == "boolean"


class TestStandardFieldMapping:
    """标准字段映射测试"""

    def test_standard_field_names_consistency(self):
        """测试标准字段名的一致性"""
        # 创建一个包含所有标准字段的 RuleDetail 实例
        detail = RuleDetail(
            rule_id="consistency_test",
            rule_key="consistency_test",
            rule_name="一致性测试",
            level1="一级",  # 标准字段名
            level2="二级",  # 标准字段名
            level3="三级",  # 标准字段名
            error_reason="原因",
            degree="程度",  # 标准字段名
            reference="参考",  # 标准字段名
            detail_position="位置",  # 标准字段名
            prompted_fields1="字段1",  # 标准字段名
            type="类型",  # 标准字段名
            pos="业务",  # 标准字段名
            applicableArea="地区",  # 标准字段名
            default_use="默认",  # 标准字段名
            start_date="2025-01-01",
            end_date="2025-12-31",
        )

        # 验证所有标准字段都可以正常访问
        assert hasattr(detail, "level1")
        assert hasattr(detail, "level2")
        assert hasattr(detail, "level3")
        assert hasattr(detail, "degree")
        assert hasattr(detail, "reference")
        assert hasattr(detail, "detail_position")
        assert hasattr(detail, "prompted_fields1")
        assert hasattr(detail, "type")
        assert hasattr(detail, "pos")
        assert hasattr(detail, "applicableArea")
        assert hasattr(detail, "default_use")

        # 验证旧字段名不存在
        assert not hasattr(detail, "error_level_1")
        assert not hasattr(detail, "error_level_2")
        assert not hasattr(detail, "error_level_3")
        assert not hasattr(detail, "error_severity")
        assert not hasattr(detail, "quality_basis")
        assert not hasattr(detail, "location_desc")
