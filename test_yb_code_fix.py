#!/usr/bin/env python3
"""
测试 yb_code 字段数据校验修复
"""

import json
import requests
import sys
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:18001"
API_KEY = "a_very_secret_key_for_development"
RULE_KEY = "test_rule_key"

def test_create_rule_detail_with_list():
    """测试使用列表格式的 yb_code 创建规则明细"""
    
    url = f"{BASE_URL}/api/v1/rules/{RULE_KEY}/details"
    headers = {
        "X-API-KEY": API_KEY,
        "Content-Type": "application/json"
    }
    
    # 测试数据 - 使用列表格式
    test_data = {
        "rule_id": "TEST_001",
        "rule_name": "测试规则 - 列表格式",
        "level1": "用药安全",
        "level2": "适应症检查", 
        "level3": "药品限制",
        "error_reason": "药品与诊断不匹配",
        "degree": "严重",
        "reference": "药品说明书",
        "detail_position": "处方明细",
        "prompted_fields1": "药品编码",
        "type": "限制性规则",
        "pos": "门诊",
        "applicableArea": "全国",
        "default_use": "是",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "yb_code": ["A01AA01", "A01AA02", "A01AA03"],  # 列表格式
        "diag_whole_code": ["K02.1", "K02.2"],
        "diag_code_prefix": ["K02"],
        "fee_whole_code": ["C001", "C002"],
        "fee_code_prefix": ["C00"]
    }
    
    print("测试1: 使用列表格式的 yb_code 创建规则明细")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 测试1通过: 列表格式创建成功")
                return True
            else:
                print(f"❌ 测试1失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 测试1失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试1异常: {str(e)}")
        return False

def test_create_rule_detail_with_string():
    """测试使用逗号分隔字符串格式的 yb_code 创建规则明细（应该失败）"""
    
    url = f"{BASE_URL}/api/v1/rules/{RULE_KEY}/details"
    headers = {
        "X-API-KEY": API_KEY,
        "Content-Type": "application/json"
    }
    
    # 测试数据 - 使用字符串格式
    test_data = {
        "rule_id": "TEST_002",
        "rule_name": "测试规则 - 字符串格式",
        "level1": "用药安全",
        "level2": "适应症检查",
        "level3": "药品限制", 
        "error_reason": "药品与诊断不匹配",
        "degree": "严重",
        "reference": "药品说明书",
        "detail_position": "处方明细",
        "prompted_fields1": "药品编码",
        "type": "限制性规则",
        "pos": "门诊",
        "applicableArea": "全国",
        "default_use": "是",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "yb_code": "A01AA01,A01AA02,A01AA03",  # 字符串格式
        "diag_whole_code": "K02.1,K02.2",
        "diag_code_prefix": "K02",
        "fee_whole_code": "C001,C002",
        "fee_code_prefix": "C00"
    }
    
    print("\n测试2: 使用字符串格式的 yb_code 创建规则明细（应该失败）")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if not result.get("success"):
                print("✅ 测试2通过: 字符串格式正确被拒绝")
                return True
            else:
                print("❌ 测试2失败: 字符串格式不应该被接受")
                return False
        else:
            print(f"❌ 测试2失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试2异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试 yb_code 字段数据校验修复...")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_create_rule_detail_with_list()
    test2_passed = test_create_rule_detail_with_string()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"测试1 (列表格式): {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"测试2 (字符串格式): {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！yb_code 字段数据校验修复成功")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)

if __name__ == "__main__":
    main()
