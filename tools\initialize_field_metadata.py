#!/usr/bin/env python3
"""
字段元数据初始化命令行工具

提供便捷的字段元数据初始化功能，支持多种初始化模式、进度显示和详细的操作报告。
"""

import argparse
import json
import sys
import time
from pathlib import Path
from typing import Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database_initializer import DatabaseInitializer  # noqa: E402
from core.db_session import get_session_factory  # noqa: E402
from core.logging.logging_system import log as logger  # noqa: E402
from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer  # noqa: E402
from tools.field_mapping_manager import FieldMappingManager  # noqa: E402


class FieldMetadataInitializerCLI:
    """字段元数据初始化命令行工具"""

    def __init__(self):
        self.args = None
        self.session_factory = None
        self.initializer = None

    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(
            description="字段元数据初始化工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
    # 完全重建字段元数据
    python tools/initialize_field_metadata.py --mode full

    # 增量更新字段元数据
    python tools/initialize_field_metadata.py --mode incremental

    # 试运行模式（不实际修改数据）
    python tools/initialize_field_metadata.py --mode full --dry-run

    # 使用自定义配置文件
    python tools/initialize_field_metadata.py --config /path/to/field_mapping.json

    # 详细输出模式
    python tools/initialize_field_metadata.py --mode full --verbose
            """
        )

        parser.add_argument(
            "--mode",
            choices=["full", "incremental"],
            default="full",
            help="初始化模式: full=完全重建, incremental=增量更新 (默认: full)"
        )

        parser.add_argument(
            "--config",
            type=str,
            help="指定field_mapping.json配置文件路径 (默认: data/field_mapping.json)"
        )

        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="试运行模式，预览操作结果但不实际修改数据"
        )

        parser.add_argument(
            "--verbose",
            action="store_true",
            help="详细输出模式，显示更多调试信息"
        )

        parser.add_argument(
            "--force",
            action="store_true",
            help="强制执行，跳过确认提示"
        )

        self.args = parser.parse_args()

    def setup_logging(self):
        """设置日志级别"""
        if self.args.verbose:
            logger.setLevel("DEBUG")
        else:
            logger.setLevel("INFO")

    def validate_config_file(self) -> bool:
        """验证配置文件"""
        config_path = self.args.config or "data/field_mapping.json"
        config_file = Path(config_path)

        if not config_file.exists():
            print(f"❌ 配置文件不存在: {config_file}")
            return False

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 基本验证
            required_keys = ["version", "rule_type_mappings", "field_definitions"]
            for key in required_keys:
                if key not in config:
                    print(f"❌ 配置文件缺少必要的键: {key}")
                    return False

            print(f"✅ 配置文件验证通过: {config_file}")
            print(f"📋 配置版本: {config.get('version', 'unknown')}")
            print(f"📋 规则类型数量: {len(config.get('rule_type_mappings', {}))}")
            return True

        except json.JSONDecodeError as e:
            print(f"❌ 配置文件JSON格式错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 验证配置文件时出错: {e}")
            return False

    def setup_database(self) -> bool:
        """设置数据库连接"""
        try:
            print("🔍 检查数据库连接...")

            # 初始化数据库
            db_initializer = DatabaseInitializer()
            success, message = db_initializer.check_and_initialize_database()

            if not success:
                print(f"❌ 数据库初始化失败: {message}")
                return False

            print(f"✅ 数据库连接成功: {message}")

            # 创建会话工厂
            self.session_factory = get_session_factory()
            print("✅ 数据库会话工厂创建成功")

            return True

        except Exception as e:
            print(f"❌ 设置数据库连接时出错: {e}")
            return False

    def setup_initializer(self) -> bool:
        """设置初始化器"""
        try:
            # 创建字段映射管理器
            config_path = self.args.config or "data/field_mapping.json"
            field_mapping_manager = FieldMappingManager(config_path)

            # 创建初始化器
            self.initializer = RuleFieldMetadataInitializer(
                self.session_factory,
                field_mapping_manager
            )

            print("✅ 字段元数据初始化器创建成功")
            return True

        except Exception as e:
            print(f"❌ 创建初始化器时出错: {e}")
            return False

    def confirm_operation(self) -> bool:
        """确认操作"""
        if self.args.force or self.args.dry_run:
            return True

        print("\n⚠️  即将执行字段元数据初始化:")
        print(f"   模式: {self.args.mode}")
        print(f"   配置文件: {self.args.config or 'data/field_mapping.json'}")

        if self.args.mode == "full":
            print("   ⚠️  完全重建模式将清除现有数据并重新创建")

        response = input("\n是否继续? (y/N): ").strip().lower()
        return response in ['y', 'yes']

    def display_progress(self, message: str):
        """显示进度信息"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

    def run_initialization(self) -> dict[str, Any]:
        """运行初始化"""
        try:
            if self.args.dry_run:
                print("\n🔍 试运行模式 - 不会实际修改数据")
                # 在试运行模式下，我们只进行验证
                with self.session_factory() as session:
                    if hasattr(self.initializer, 'validation_engine'):
                        validation_engine = self.initializer.validation_engine
                    else:
                        from services.rule_field_metadata_initializer import ValidationEngine
                        validation_engine = ValidationEngine(
                            self.initializer.field_mapping_manager, 
                            session
                        )

                    validation_report = validation_engine.generate_validation_report()
                    return {
                        "dry_run": True,
                        "validation_report": validation_report,
                        "message": "试运行完成，未修改任何数据"
                    }
            else:
                print(f"\n🚀 开始执行字段元数据初始化 (模式: {self.args.mode})")
                self.display_progress("正在初始化字段元数据...")

                # 执行实际初始化
                result = self.initializer.initialize_all_metadata(mode=self.args.mode)

                self.display_progress("初始化完成")
                return result

        except Exception as e:
            logger.error(f"初始化过程中出错: {e}", exc_info=True)
            return {
                "error": str(e),
                "success": False
            }

    def generate_report(self, result: dict[str, Any]):
        """生成详细报告"""
        print("\n" + "="*60)
        print("📊 字段元数据初始化报告")
        print("="*60)

        if result.get("dry_run"):
            print("🔍 模式: 试运行")
            validation_report = result.get("validation_report", {})
            print(f"📋 验证状态: {validation_report.get('overall_status', 'UNKNOWN')}")
            print(f"❌ 错误数量: {validation_report.get('total_errors', 0)}")
            print(f"⚠️  警告数量: {validation_report.get('total_warnings', 0)}")

            if validation_report.get('total_errors', 0) > 0:
                print("\n❌ 发现的错误:")
                for category, details in validation_report.get('validation_results', {}).items():
                    if details.get('errors'):
                        print(f"  {category}:")
                        for error in details['errors']:
                            print(f"    - {error}")

            return

        if result.get("error"):
            print(f"❌ 初始化失败: {result['error']}")
            return

        # 显示统计信息
        print(f"⏱️  执行时间: {result.get('start_time', 'N/A')} - {result.get('end_time', 'N/A')}")
        print(f"⏱️  执行时长: {result.get('duration_seconds', 0):.2f} 秒")
        print(f"📋 规则类型总数: {result.get('total_rule_types', 0)}")
        print(f"✅ 创建模板数: {result.get('created_templates', 0)}")
        print(f"🔄 更新模板数: {result.get('updated_templates', 0)}")
        print(f"✅ 创建字段元数据: {result.get('created_field_metadata', 0)}")
        print(f"🔄 更新字段元数据: {result.get('updated_field_metadata', 0)}")
        print(f"❌ 错误数量: {result.get('total_errors', 0)}")
        print(f"⚠️  警告数量: {result.get('total_warnings', 0)}")

        # 显示错误详情
        if result.get('errors'):
            print("\n❌ 错误详情:")
            for i, error in enumerate(result['errors'], 1):
                print(f"  {i}. {error}")

        # 显示警告详情
        if result.get('warnings'):
            print("\n⚠️  警告详情:")
            for i, warning in enumerate(result['warnings'], 1):
                print(f"  {i}. {warning}")

        # 总结
        if result.get('total_errors', 0) == 0:
            print("\n🎉 初始化成功完成！")
        else:
            print(f"\n⚠️  初始化完成，但发现 {result.get('total_errors', 0)} 个错误")

    def run(self):
        """运行命令行工具"""
        try:
            # 解析参数
            self.parse_arguments()

            # 设置日志
            self.setup_logging()

            print("🚀 字段元数据初始化工具")
            print("="*40)

            # 验证配置文件
            if not self.validate_config_file():
                sys.exit(1)

            # 设置数据库
            if not self.setup_database():
                sys.exit(1)

            # 设置初始化器
            if not self.setup_initializer():
                sys.exit(1)

            # 确认操作
            if not self.confirm_operation():
                print("❌ 操作已取消")
                sys.exit(0)

            # 运行初始化
            result = self.run_initialization()

            # 生成报告
            self.generate_report(result)

            # 退出码
            if result.get("error") or result.get('total_errors', 0) > 0:
                sys.exit(1)
            else:
                sys.exit(0)

        except KeyboardInterrupt:
            print("\n❌ 操作被用户中断")
            sys.exit(1)
        except Exception as e:
            print(f"❌ 程序执行出错: {e}")
            logger.error(f"CLI工具执行出错: {e}", exc_info=True)
            sys.exit(1)


def main():
    """主函数"""
    cli = FieldMetadataInitializerCLI()
    cli.run()


if __name__ == "__main__":
    main()
