"""Initial migration with all tables

Revision ID: 94713ed230f9
Revises:
Create Date: 2025-07-22 17:22:38.153149

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "94713ed230f9"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "base_rules",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("rule_key", sa.String(length=100), nullable=False),
        sa.Column("rule_name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "module_path", sa.String(length=255), nullable=False, comment="Python module path for the rule class"
        ),
        sa.Column("file_hash", sa.String(length=64), nullable=False, comment="SHA-256 hash of the rule file content"),
        sa.Column(
            "status",
            sa.Enum("NEW", "CHANGED", "READY", "DEPRECATED", name="rulestatusenum"),
            nullable=False,
            comment="The lifecycle status of the rule: NEW, CHANGED, READY, DEPRECATED",
        ),
        sa.Column("template_spec", sa.JSON(), nullable=True, comment="Specification for the Excel template"),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_base_rules_id"), "base_rules", ["id"], unique=False)
    op.create_index(op.f("ix_base_rules_rule_key"), "base_rules", ["rule_key"], unique=True)
    op.create_table(
        "registration_task_status",
        sa.Column("task_id", sa.String(length=36), nullable=False, comment="任务ID"),
        sa.Column("task_type", sa.String(length=50), nullable=False, comment="任务类型"),
        sa.Column("rule_key", sa.String(length=255), nullable=False, comment="规则键值"),
        sa.Column("user_id", sa.String(length=255), nullable=True, comment="用户ID"),
        sa.Column("status", sa.String(length=20), nullable=False, comment="任务状态"),
        sa.Column("total_operations", sa.Integer(), nullable=True, comment="总操作数"),
        sa.Column("completed_operations", sa.Integer(), nullable=True, comment="已完成操作数"),
        sa.Column("progress_percentage", sa.Numeric(precision=5, scale=2), nullable=True, comment="进度百分比"),
        sa.Column("current_message", sa.Text(), nullable=True, comment="当前消息"),
        sa.Column("error_message", sa.Text(), nullable=True, comment="错误消息"),
        sa.Column("result_data", sa.JSON(), nullable=True, comment="结果数据"),
        sa.Column("stats", sa.JSON(), nullable=True, comment="统计信息"),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True, comment="创建时间"
        ),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=True, comment="开始时间"),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True, comment="完成时间"),
        sa.Column("execution_time", sa.Numeric(precision=10, scale=3), nullable=True, comment="执行时间(秒)"),
        sa.Column(
            "updated_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True, comment="更新时间"
        ),
        sa.Column("transaction_steps", sa.JSON(), nullable=True, comment="事务步骤状态"),
        sa.Column("compensation_needed", sa.Boolean(), nullable=True, comment="是否需要补偿操作"),
        sa.Column("last_step_completed", sa.String(length=50), nullable=True, comment="最后完成的步骤"),
        sa.PrimaryKeyConstraint("task_id"),
        comment="注册任务状态表",
    )
    op.create_table(
        "rule_data_sets",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("base_rule_id", sa.Integer(), nullable=False),
        sa.Column(
            "data_set",
            sa.JSON(),
            nullable=True,
            comment="The actual rule data from the uploaded Excel (deprecated, use rule_details)",
        ),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column(
            "is_active", sa.Boolean(), nullable=False, comment="Indicates if this data set is the one to be used"
        ),
        sa.Column("uploaded_by", sa.String(length=100), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column(
            "migration_status",
            sa.Enum("PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", name="migrationstatusenum"),
            nullable=True,
            comment="数据迁移状态",
        ),
        sa.Column("migration_timestamp", sa.DateTime(), nullable=True, comment="迁移时间戳"),
        sa.ForeignKeyConstraint(
            ["base_rule_id"],
            ["base_rules.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_rule_data_sets_id"), "rule_data_sets", ["id"], unique=False)
    op.create_table(
        "rule_details",
        sa.Column("id", sa.BigInteger(), autoincrement=True, nullable=False),
        sa.Column("dataset_id", sa.Integer(), nullable=False),
        sa.Column("rule_detail_id", sa.String(length=100), nullable=False, comment="业务规则ID（原 rule_id）"),
        sa.Column("rule_name", sa.String(length=255), nullable=False, comment="规则名称"),
        sa.Column("error_level_1", sa.String(length=100), nullable=True, comment="一级错误类型"),
        sa.Column("error_level_2", sa.String(length=100), nullable=True, comment="二级错误类型"),
        sa.Column("error_level_3", sa.String(length=100), nullable=True, comment="三级错误类型"),
        sa.Column("error_reason", sa.Text(), nullable=True, comment="错误原因"),
        sa.Column("error_severity", sa.String(length=50), nullable=True, comment="错误程度"),
        sa.Column("quality_basis", sa.Text(), nullable=True, comment="质控依据或参考资料"),
        sa.Column("location_desc", sa.Text(), nullable=True, comment="具体位置描述"),
        sa.Column("prompt_field_type", sa.String(length=100), nullable=True, comment="提示字段类型"),
        sa.Column("prompt_field_code", sa.String(length=100), nullable=True, comment="提示字段编码"),
        sa.Column("prompt_field_seq", sa.Integer(), nullable=True, comment="提示字段序号"),
        sa.Column("rule_category", sa.String(length=100), nullable=True, comment="规则类别"),
        sa.Column("applicable_business", sa.String(length=100), nullable=True, comment="适用业务"),
        sa.Column("applicable_region", sa.String(length=100), nullable=True, comment="适用地区"),
        sa.Column("default_selected", sa.Boolean(), nullable=True, comment="默认选用"),
        sa.Column("involved_amount", sa.DECIMAL(precision=15, scale=2), nullable=True, comment="涉及金额"),
        sa.Column("usage_quantity", sa.Integer(), nullable=True, comment="使用数量"),
        sa.Column("violation_quantity", sa.Integer(), nullable=True, comment="违规数量"),
        sa.Column("usage_days", sa.Integer(), nullable=True, comment="使用天数"),
        sa.Column("violation_days", sa.Integer(), nullable=True, comment="违规天数"),
        sa.Column("violation_items", sa.Text(), nullable=True, comment="违规项目"),
        sa.Column("effective_start_time", sa.DateTime(), nullable=True, comment="生效开始时间"),
        sa.Column("effective_end_time", sa.DateTime(), nullable=True, comment="生效结束时间"),
        sa.Column("extra_data", sa.JSON(), nullable=True, comment="扩展数据字段，用于存储未来可能新增的字段"),
        sa.Column("remarks", sa.Text(), nullable=True, comment="备注信息"),
        sa.Column(
            "status",
            sa.Enum("ACTIVE", "INACTIVE", "DELETED", name="ruledetailstatusenum"),
            nullable=True,
            comment="记录状态",
        ),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column("created_by", sa.String(length=100), nullable=True, comment="创建人"),
        sa.Column("updated_by", sa.String(length=100), nullable=True, comment="更新人"),
        sa.ForeignKeyConstraint(["dataset_id"], ["rule_data_sets.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_rule_details_id"), "rule_details", ["id"], unique=False)

    # Create additional indexes for rule_details table
    op.create_index("idx_dataset_status", "rule_details", ["dataset_id", "status"], unique=False)
    op.create_index("idx_created_at", "rule_details", ["created_at"], unique=False)
    op.create_index("idx_updated_at", "rule_details", ["updated_at"], unique=False)

    # Create unique constraint for rule_details
    op.create_unique_constraint("uk_dataset_rule_detail", "rule_details", ["dataset_id", "rule_detail_id"])

    # Create indexes for registration_task_status table
    op.create_index("idx_registration_task_status_rule_key", "registration_task_status", ["rule_key"])
    op.create_index("idx_registration_task_status_status", "registration_task_status", ["status"])
    op.create_index("idx_registration_task_status_created_at", "registration_task_status", ["created_at"])
    op.create_index("idx_registration_task_status_user_id", "registration_task_status", ["user_id"])
    op.create_index("idx_registration_task_status_updated_at", "registration_task_status", ["updated_at"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop rule_details table and its indexes/constraints
    # Note: Drop table directly, which will automatically drop all indexes and constraints
    op.drop_table("rule_details")

    # Drop rule_data_sets table and its indexes
    op.drop_index(op.f("ix_rule_data_sets_id"), table_name="rule_data_sets")
    op.drop_table("rule_data_sets")

    # Drop registration_task_status table and its indexes
    # Note: Drop table directly, which will automatically drop all indexes
    op.drop_table("registration_task_status")

    # Drop base_rules table and its indexes
    # Note: Drop table directly, which will automatically drop all indexes
    op.drop_table("base_rules")
    # ### end Alembic commands ###
