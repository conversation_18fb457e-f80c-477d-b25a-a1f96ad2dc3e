#!/usr/bin/env python3
"""
子节点数据加载重构测试套件运行脚本
集成运行所有测试并生成完整的质量报告
"""

import argparse
import subprocess
import sys
import time
from pathlib import Path


def run_command(cmd: list, description: str, capture_output: bool = True) -> tuple:
    """运行命令并返回结果"""
    print(f"[RUN] {description}...")

    try:
        if capture_output:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=Path(__file__).parent
            )
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(cmd, cwd=Path(__file__).parent)
            return result.returncode == 0, "", ""
    except Exception as e:
        return False, "", str(e)


def run_unit_tests(verbose: bool = False) -> bool:
    """运行单元测试"""
    print("\n" + "="*50)
    print("[TEST] 运行单元测试")
    print("="*50)

    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/unit/",
        "-v" if verbose else "-q",
        "--tb=short",
        "--disable-warnings"
    ]

    success, stdout, stderr = run_command(cmd, "执行单元测试", capture_output=False)

    if success:
        print("[PASS] 单元测试执行完成")
    else:
        print("[FAIL] 单元测试执行失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def run_integration_tests(verbose: bool = False) -> bool:
    """运行集成测试"""
    print("\n" + "="*50)
    print("[INTEGRATION] 运行集成测试")
    print("="*50)

    cmd = [
        sys.executable, "-m", "pytest",
        "tests/integration/",
        "-v" if verbose else "-q",
        "--tb=short",
        "--disable-warnings"
    ]

    success, stdout, stderr = run_command(cmd, "执行集成测试", capture_output=False)

    if success:
        print("[PASS] 集成测试执行完成")
    else:
        print("[FAIL] 集成测试执行失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def run_e2e_tests(verbose: bool = False) -> bool:
    """运行端到端测试"""
    print("\n" + "="*50)
    print("🌐 运行端到端测试")
    print("="*50)

    cmd = [
        sys.executable, "-m", "pytest",
        "tests/e2e/",
        "-v" if verbose else "-q",
        "--tb=short",
        "--disable-warnings"
    ]

    success, stdout, stderr = run_command(cmd, "执行端到端测试", capture_output=False)

    if success:
        print("[PASS] 端到端测试执行完成")
    else:
        print("[FAIL] 端到端测试执行失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def run_performance_tests(verbose: bool = False) -> bool:
    """运行性能测试"""
    print("\n" + "="*50)
    print("[PERF] 运行性能测试")
    print("="*50)

    cmd = [
        sys.executable, "-m", "pytest",
        "tests/performance/",
        "-v" if verbose else "-q",
        "--tb=short",
        "--disable-warnings",
        "-s"  # 显示print输出，用于查看性能指标
    ]

    success, stdout, stderr = run_command(cmd, "执行性能测试", capture_output=False)

    if success:
        print("[PASS] 性能测试执行完成")
    else:
        print("[FAIL] 性能测试执行失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def run_coverage_analysis() -> bool:
    """运行代码覆盖率分析"""
    print("\n" + "="*50)
    print("[COV] 运行代码覆盖率分析")
    print("="*50)

    # 检查是否安装了coverage
    try:
        import coverage
    except ImportError:
        print("[WARN] 未安装coverage包，跳过覆盖率分析")
        print("   安装命令: pip install coverage")
        return True

    # 运行带覆盖率的测试
    cmd = [
        sys.executable, "-m", "coverage", "run",
        "--source=services,core,models,api",
        "--omit=*/tests/*,*/test_*",
        "-m", "pytest",
        "tests/unit/",
        "tests/integration/",
        "-q",
        "--disable-warnings"
    ]

    success, stdout, stderr = run_command(cmd, "执行覆盖率测试")

    if success:
        # 生成覆盖率报告
        print("🔄 生成覆盖率报告...")

        # 控制台报告
        run_command([sys.executable, "-m", "coverage", "report"], "生成控制台覆盖率报告", capture_output=False)

        # HTML报告
        html_success, _, _ = run_command([sys.executable, "-m", "coverage", "html", "-d", "reports/coverage_html"], "生成HTML覆盖率报告")

        if html_success:
            print("[PASS] HTML覆盖率报告已生成: reports/coverage_html/index.html")

        print("[PASS] 代码覆盖率分析完成")
    else:
        print("[FAIL] 代码覆盖率分析失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def run_code_quality_checks() -> bool:
    """运行代码质量检查"""
    print("\n" + "="*50)
    print("🔍 运行代码质量检查")
    print("="*50)

    checks_passed = 0
    total_checks = 0

    # 检查ruff（代码格式和lint）
    try:
        import ruff
        total_checks += 1

        print("🔄 运行ruff代码检查...")
        cmd = [sys.executable, "-m", "ruff", "check", "services/", "core/", "models/", "api/", "--quiet"]
        success, stdout, stderr = run_command(cmd, "执行ruff检查")

        if success:
            print("[PASS] ruff代码检查通过")
            checks_passed += 1
        else:
            print("[FAIL] ruff代码检查发现问题")
            if stderr:
                print(f"   问题详情: {stderr}")

    except ImportError:
        print("[WARN] 未安装ruff，跳过代码格式检查")
        print("   安装命令: pip install ruff")

    # 检查mypy（类型检查）
    try:
        import mypy
        total_checks += 1

        print("🔄 运行mypy类型检查...")
        cmd = [sys.executable, "-m", "mypy", "services/", "--ignore-missing-imports", "--quiet"]
        success, stdout, stderr = run_command(cmd, "执行mypy检查")

        if success or "error" not in stderr.lower():
            print("[PASS] mypy类型检查通过")
            checks_passed += 1
        else:
            print("[FAIL] mypy类型检查发现问题")
            if stderr:
                print(f"   问题详情: {stderr}")

    except ImportError:
        print("[WARN] 未安装mypy，跳过类型检查")
        print("   安装命令: pip install mypy")

    # 检查bandit（安全扫描）
    try:
        import bandit
        total_checks += 1

        print("🔄 运行bandit安全扫描...")
        cmd = [sys.executable, "-m", "bandit", "-r", "services/", "-f", "txt", "-q"]
        success, stdout, stderr = run_command(cmd, "执行bandit安全扫描")

        if success:
            print("[PASS] bandit安全扫描通过")
            checks_passed += 1
        else:
            print("[FAIL] bandit安全扫描发现问题")
            if stderr:
                print(f"   问题详情: {stderr}")

    except ImportError:
        print("[WARN] 未安装bandit，跳过安全扫描")
        print("   安装命令: pip install bandit")

    if total_checks == 0:
        print("[WARN] 没有可用的代码质量检查工具")
        return True

    success_rate = checks_passed / total_checks
    print(f"\n📋 代码质量检查结果: {checks_passed}/{total_checks} 项通过 ({success_rate:.1%})")

    return success_rate >= 0.8  # 80%以上通过率视为成功


def generate_test_report() -> bool:
    """生成测试报告"""
    print("\n" + "="*50)
    print("📄 生成测试质量报告")
    print("="*50)

    cmd = [sys.executable, "tests/test_coverage_report.py"]
    success, stdout, stderr = run_command(cmd, "生成测试报告", capture_output=False)

    if success:
        print("[PASS] 测试质量报告生成完成")
        print("   查看报告: reports/latest_test_report.html")
    else:
        print("[FAIL] 测试质量报告生成失败")
        if stderr:
            print(f"错误信息: {stderr}")

    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="子节点数据加载重构测试套件")
    parser.add_argument("--unit", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration", action="store_true", help="只运行集成测试")
    parser.add_argument("--e2e", action="store_true", help="只运行端到端测试")
    parser.add_argument("--performance", action="store_true", help="只运行性能测试")
    parser.add_argument("--coverage", action="store_true", help="只运行覆盖率分析")
    parser.add_argument("--quality", action="store_true", help="只运行代码质量检查")
    parser.add_argument("--report", action="store_true", help="只生成测试报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--quick", "-q", action="store_true", help="快速模式（跳过性能测试）")

    args = parser.parse_args()

    print("子节点数据加载重构 - 测试套件运行器")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {Path.cwd()}")
    print("=" * 60)

    start_time = time.time()
    results = {}

    # 创建报告目录
    Path("reports").mkdir(exist_ok=True)

    # 根据参数决定运行哪些测试
    if args.unit:
        results["unit_tests"] = run_unit_tests(args.verbose)
    elif args.integration:
        results["integration_tests"] = run_integration_tests(args.verbose)
    elif args.e2e:
        results["e2e_tests"] = run_e2e_tests(args.verbose)
    elif args.performance:
        results["performance_tests"] = run_performance_tests(args.verbose)
    elif args.coverage:
        results["coverage"] = run_coverage_analysis()
    elif args.quality:
        results["quality"] = run_code_quality_checks()
    elif args.report:
        results["report"] = generate_test_report()
    else:
        # 运行完整测试套件
        print("🚀 运行完整测试套件...")

        results["unit_tests"] = run_unit_tests(args.verbose)
        results["integration_tests"] = run_integration_tests(args.verbose)
        results["e2e_tests"] = run_e2e_tests(args.verbose)

        if not args.quick:
            results["performance_tests"] = run_performance_tests(args.verbose)

        results["coverage"] = run_coverage_analysis()
        results["quality"] = run_code_quality_checks()
        results["report"] = generate_test_report()

    # 总结结果
    total_time = time.time() - start_time

    print("\n" + "="*60)
    print("[SUMMARY] 测试套件执行总结")
    print("="*60)

    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)

    for test_name, passed in results.items():
        status = "[PASS]" if passed else "[FAIL]"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")

    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 项测试通过")
    print(f"⏱️  总耗时: {total_time:.1f} 秒")

    if passed_tests == total_tests:
        print("\n🎉 恭喜！所有测试均通过！")
        print("✨ 子节点数据加载重构的质量目标已达成：")
        print("   ✓ 单元测试覆盖率 ≥ 90%")
        print("   ✓ 集成测试覆盖关键业务流程")
        print("   ✓ 性能测试验证90%+响应时间提升")
        print("   ✓ 负载测试验证系统稳定性")
        exit_code = 0
    else:
        print(f"\n[WARN] {total_tests - passed_tests} 项测试未通过")
        print("📋 请查看上述详细信息并进行修复")
        exit_code = 1

    print("\n📄 查看详细报告:")
    print("   HTML报告: reports/latest_test_report.html")
    print("   覆盖率报告: reports/coverage_html/index.html")
    print("   原始数据: reports/latest_test_report.json")

    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
