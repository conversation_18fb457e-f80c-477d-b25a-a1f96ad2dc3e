"""
Rule management router for master node.
"""

import inspect
from datetime import datetime
from pathlib import Path
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from api.dependencies.auth import get_api_key_dependency
from core.db_pool_manager import get_pool_manager
from core.db_session import get_db_pool_status, get_db_session
from core.logging.logging_system import log as logger
from models.api import ApiResponse, ConfirmSubmissionRequest, TaskListResponse, TaskStatusResponse
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from services.rule_change_detector import RuleChangeDetector
from services.rule_loader import load_rules_into_cache
from services.rule_query_service import RuleQueryService
from services.task_status_manager import TaskStatus, get_task_status_manager
from services.template_generator import TemplateGenerator

# Management router (requires API key authentication)
management_router = APIRouter(
    prefix="/api/v1/rules",
    tags=["Rule Management (Secure)"],
    dependencies=[get_api_key_dependency()],
)


@management_router.get("/status", response_model=ApiResponse[list[dict[str, Any]]])
def get_all_rules_status(session: Session = Depends(get_db_session)):
    """
    Get status of all rules in the system.

    Args:
        session: Database session dependency

    Returns:
        ApiResponse[list[dict]]: 统一格式的API响应，包含规则状态信息列表
    """
    try:
        logger.info("获取所有规则状态列表")

        query = select(
            RuleTemplate.rule_key,
            RuleTemplate.name,
            RuleTemplate.description,
            RuleTemplate.status,
            RuleTemplate.updated_at,
        ).order_by(RuleTemplate.rule_key)

        result = session.execute(query)
        rules_data = [dict(row) for row in result.mappings().all()]

        logger.info(f"成功获取规则状态列表，共 {len(rules_data)} 条规则")

        return ApiResponse.success_response(
            data=rules_data, message=f"成功获取规则状态列表，共 {len(rules_data)} 条规则"
        )

    except Exception as e:
        logger.error(f"获取规则状态列表失败: {str(e)}", exc_info=True)
        return ApiResponse.error_response(code=500, message="获取规则状态列表失败，请稍后重试")


@management_router.get("/ids", response_model=ApiResponse[dict[str, Any]])
def get_rule_ids_by_keyword(keyword: str = None, session: Session = Depends(get_db_session)):
    """
    根据关键词模糊匹配规则，返回匹配规则下所有生效明细规则的ID列表

    Args:
        keyword: 用于模糊匹配的关键词（可选）
        session: 数据库会话依赖

    Returns:
        ApiResponse[dict]: 统一格式的API响应，包含规则信息和明细规则ID列表

    Raises:
        HTTPException: 当查询失败时抛出500错误
    """
    try:
        logger.info(f"根据关键词搜索规则: keyword={keyword}")

        # 调用服务层进行查询
        result = RuleQueryService.search_rules_by_keyword(session, keyword)

        # 根据服务层返回结果构建响应
        if result["success"]:
            return ApiResponse.success_response(data=result["data"], message=result["message"])
        else:
            return ApiResponse.error_response(code=404, message=result["message"])

    except Exception as e:
        logger.error(f"规则ID查询接口异常: keyword='{keyword}', 错误={str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="查询规则ID时发生内部错误") from e


@management_router.get("/{rule_key}/template")
async def download_template(rule_key: str, session: Session = Depends(get_db_session)):
    """
    下载Excel模板（直接返回预生成文件）

    Args:
        rule_key: 规则键
        session: 数据库会话

    Returns:
        FileResponse: Excel模板文件

    Raises:
        HTTPException: 如果规则模板或文件不存在
    """
    try:
        # 1. 检查规则模板是否存在
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(status_code=404, detail=f"规则模板 '{rule_key}' 不存在")

        # 2. 获取预生成的模板文件
        from services.template_pre_generation_service import TemplateFileManager

        file_manager = TemplateFileManager("generated_templates")
        template_file = file_manager.get_template_file(rule_key)

        # 3. 如果文件不存在，触发即时生成
        if not template_file or not template_file.exists():
            logger.warning(f"模板文件不存在，触发即时生成: {rule_key}")
            await _generate_template_immediately(rule_key, session)
            template_file = file_manager.get_template_file(rule_key)

        # 4. 如果仍然没有文件，返回错误
        if not template_file or not template_file.exists():
            raise HTTPException(status_code=404, detail=f"模板文件生成失败: {rule_key}")

        # 5. 返回文件
        filename = f"{template.name}规则模板.xlsx"
        return FileResponse(
            path=template_file,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载模板失败: rule_key={rule_key}, error={e}")
        raise HTTPException(status_code=500, detail="下载模板失败")


async def _generate_template_immediately(rule_key: str, session: Session):
    """即时生成模板（当预生成文件不存在时）"""
    try:
        from services.template_pre_generation_service import TemplatePreGenerationService

        service = TemplatePreGenerationService(session)
        await service.regenerate_template(rule_key)
        logger.info(f"即时生成模板成功: {rule_key}")

    except Exception as e:
        logger.error(f"即时生成模板失败: rule_key={rule_key}, error={e}")
        raise


@management_router.post("/admin/templates/regenerate")
async def regenerate_all_templates(session: Session = Depends(get_db_session)):
    """手动触发重新生成所有模板"""
    try:
        from services.template_pre_generation_service import TemplatePreGenerationService

        service = TemplatePreGenerationService(session)
        result = await service.generate_all_templates_on_startup()

        return {"success": True, "message": "所有模板重新生成完成", "data": result}

    except Exception as e:
        logger.error(f"重新生成所有模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"重新生成模板失败: {str(e)}")


@management_router.get("/admin/templates/status")
async def get_template_status(session: Session = Depends(get_db_session)):
    """获取模板生成状态"""
    try:
        from services.template_pre_generation_service import TemplatePreGenerationService

        service = TemplatePreGenerationService(session)
        status = service.get_template_status()

        return {"success": True, "data": status, "total": len(status)}

    except Exception as e:
        logger.error(f"获取模板状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模板状态失败: {str(e)}")


@management_router.post("/admin/templates/{rule_key}/regenerate")
async def regenerate_single_template(rule_key: str, session: Session = Depends(get_db_session)):
    """重新生成指定模板"""
    try:
        from services.template_pre_generation_service import TemplatePreGenerationService

        service = TemplatePreGenerationService(session)
        result = await service.regenerate_template(rule_key)

        return {"success": True, "message": f"模板 '{rule_key}' 重新生成完成", "generated": result}

    except Exception as e:
        logger.error(f"重新生成模板失败: rule_key={rule_key}, error={e}")
        raise HTTPException(status_code=500, detail=f"重新生成模板失败: {str(e)}")


@management_router.get("/{rule_key}/schema", response_model=list[dict[str, Any]])
def get_rule_schema(rule_key: str):
    """
    Get schema information for a specific rule.

    Args:
        rule_key: The unique key of the rule

    Returns:
        list[dict]: Schema information for the rule parameters

    Raises:
        HTTPException: If rule class not found or schema generation fails
    """
    try:
        rule_class, _, _ = RuleChangeDetector._get_rule_class_info(f"rules.base_rules.{rule_key}")

        if not rule_class:
            raise HTTPException(status_code=404, detail=f"Rule class for key '{rule_key}' not found.")

        param_mapping = TemplateGenerator.get_parameter_mapping(rule_class)
        signature = inspect.signature(rule_class.__init__)
        schema = []
        base_params = ("rule_id", "self")

        for name, param in signature.parameters.items():
            if name in base_params:
                continue

            param_type = "any"
            if param.annotation is not inspect.Parameter.empty:
                param_type = str(param.annotation)

            schema.append(
                {
                    "name_en": name,
                    "name_cn": param_mapping.get(name, name),
                    "type": param_type,
                    "required": "None" not in param_type,
                }
            )

        return schema

    except Exception as e:
        logger.exception(f"Error generating schema for '{rule_key}'")
        raise HTTPException(status_code=500, detail="Failed to generate schema.") from e


@management_router.get("/{rule_key}/detail", response_model=dict[str, Any])
def get_rule_template_detail(rule_key: str, session: Session = Depends(get_db_session)):
    """
    Get complete rule template detail information including parameters and active rule data.

    Args:
        rule_key: The unique key of the rule
        session: Database session dependency

    Returns:
        dict: Complete rule template detail information

    Raises:
        HTTPException: If rule not found or detail generation fails
    """
    try:
        # 1. 获取规则模板信息
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(status_code=404, detail=f"Rule template '{rule_key}' not found.")

        # 2. 获取规则参数信息（复用现有的schema逻辑）
        rule_class, _, _ = RuleChangeDetector._get_rule_class_info(f"rules.base_rules.{rule_key}")

        parameters = []
        if rule_class:
            param_mapping = TemplateGenerator.get_parameter_mapping(rule_class)
            signature = inspect.signature(rule_class.__init__)
            base_params = ("rule_id", "self")

            for name, param in signature.parameters.items():
                if name in base_params:
                    continue

                param_type = "any"
                if param.annotation is not inspect.Parameter.empty:
                    param_type = str(param.annotation)

                parameters.append(
                    {
                        "name_en": name,
                        "name_cn": param_mapping.get(name, name),
                        "type": param_type,
                        "required": "None" not in param_type,
                    }
                )

        # 3. 获取规则明细列表
        rule_details = (
            session.query(RuleDetail)
            .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE))
            .order_by(RuleDetail.id)
            .all()
        )

        # 4. 构建规则明细信息
        active_rules = []
        for detail in rule_details:
            rule_data = {
                "rule_id": detail.rule_id,
                "rule_name": detail.rule_name,
                "level1": detail.level1,
                "level2": detail.level2,
                "level3": detail.level3,
                "error_reason": detail.error_reason,
                "degree": detail.degree,
                "reference": detail.reference,
                "status": detail.status.value,
                "created_at": detail.created_at.isoformat() if detail.created_at else None,
                "updated_at": detail.updated_at.isoformat() if detail.updated_at else None,
            }

            # 添加扩展字段
            if detail.extended_fields:
                try:
                    import json

                    extended_data = (
                        json.loads(detail.extended_fields)
                        if isinstance(detail.extended_fields, str)
                        else detail.extended_fields
                    )
                    rule_data["extended_fields"] = extended_data
                except Exception as e:
                    logger.warning(f"Failed to parse extended fields for rule {detail.rule_id}: {e}")

            active_rules.append(rule_data)

        # 5. 构建完整的详情响应
        detail_info = {
            "rule_key": template.rule_key,
            "rule_name": template.name,
            "description": template.description,
            "status": template.status.value,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None,
            "parameters": parameters,
            "active_rules": active_rules,
            "active_rules_count": len(active_rules),
        }

        return detail_info

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting rule template detail for '{rule_key}'")
        raise HTTPException(status_code=500, detail="Failed to get rule template detail.") from e


@management_router.post("/{rule_key}/confirm_submission", status_code=status.HTTP_201_CREATED)
async def confirm_submission(
    rule_key: str, submission: ConfirmSubmissionRequest, session: Session = Depends(get_db_session)
):
    """
    Confirm and submit rule data.

    重构版本：使用新的三表结构存储规则明细数据。

    Args:
        rule_key: The unique key of the rule
        submission: The submission request with data to save
        session: Database session dependency

    Returns:
        dict: Success message with storage information

    Raises:
        HTTPException: If rule not found or submission fails
    """
    try:
        from services.rule_detail_service import RuleDetailService
        from services.unified_data_mapping_engine import UnifiedDataMappingEngine

        # 1. 验证规则模板存在性
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(status_code=404, detail=f"Rule template '{rule_key}' not found.")

        # 2. 初始化服务
        rule_service = RuleDetailService(session)
        mapping_engine = UnifiedDataMappingEngine()

        # 3. 处理提交的数据
        success_count = 0
        failed_count = 0
        errors = []

        if submission.data_to_submit:
            for i, rule_data in enumerate(submission.data_to_submit):
                try:
                    # 标准化字段名
                    normalized_data = mapping_engine.normalize_field_names(rule_data)
                    normalized_data["rule_key"] = rule_key

                    # 创建规则明细
                    rule_service.create_rule_detail(rule_key, normalized_data)
                    success_count += 1

                except Exception as e:
                    failed_count += 1
                    errors.append(f"Row {i+1}: {str(e)}")
                    logger.warning(f"Failed to create rule detail for row {i+1}: {e}")

        # 4. 更新模板状态
        template.status = RuleTemplateStatusEnum.READY
        session.add(template)
        session.commit()

        logger.info(
            f"Successfully submitted {success_count} rule details for rule '{rule_key}'. "
            f"Failed: {failed_count}. Status set to READY."
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during submission for '{rule_key}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An internal error occurred during data submission.") from e

    # Reload rules into cache
    load_rules_into_cache(session)

    # 构建响应
    response = {
        "message": f"Successfully submitted and activated new data for rule '{rule_key}'.",
        "total_items": len(submission.data_to_submit) if submission.data_to_submit else 0,
        "success_count": success_count,
        "failed_count": failed_count,
        "storage_mode": "structured",
    }

    if errors:
        response["errors"] = errors

    return response


@management_router.get("/db-pool/status", response_model=dict[str, Any])
def get_database_pool_status():
    """
    Get database connection pool status.

    Returns:
        dict: Database connection pool status information
    """
    try:
        pool_status = get_db_pool_status()

        # 添加额外的统计信息
        pool_manager = get_pool_manager()
        if pool_manager:
            stats = pool_manager.get_stats()
            current_metrics = pool_manager.get_current_metrics()

            pool_status.update(
                {
                    "stats": {
                        "adjustment_count": stats.adjustment_count,
                        "leak_detection_count": stats.leak_detection_count,
                        "last_adjustment_time": stats.last_adjustment_time,
                    },
                    "current_metrics": {
                        "pool_utilization": current_metrics.pool_utilization if current_metrics else 0.0,
                        "timestamp": current_metrics.timestamp if current_metrics else 0.0,
                    },
                }
            )

        return pool_status

    except Exception as e:
        logger.error(f"Error getting database pool status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get database pool status") from e


@management_router.post("/db-pool/adjust", status_code=status.HTTP_200_OK)
def adjust_database_pool_size(target_size: int):
    """
    Manually adjust database connection pool size.

    Args:
        target_size: Target pool size

    Returns:
        dict: Success message

    Raises:
        HTTPException: If adjustment fails or invalid parameters
    """
    try:
        if target_size < 1 or target_size > 100:
            raise HTTPException(status_code=400, detail="Target size must be between 1 and 100")

        pool_manager = get_pool_manager()
        if not pool_manager:
            raise HTTPException(status_code=503, detail="Database pool manager not available")

        # 手动调整连接池大小
        pool_manager._resize_pool(target_size)

        logger.info(f"Database pool size manually adjusted to {target_size}")

        return {"message": f"Database pool size adjusted to {target_size}", "target_size": target_size}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adjusting database pool size: {e}")
        raise HTTPException(status_code=500, detail="Failed to adjust database pool size") from e


@management_router.post("/db-pool/warmup", status_code=status.HTTP_200_OK)
def warmup_database_pool(target_connections: int = None):
    """
    Warm up database connection pool.

    Args:
        target_connections: Number of connections to create (optional)

    Returns:
        dict: Success message

    Raises:
        HTTPException: If warmup fails
    """
    try:
        pool_manager = get_pool_manager()
        if not pool_manager:
            raise HTTPException(status_code=503, detail="Database pool manager not available")

        # 预热连接池
        pool_manager.warm_up_pool(target_connections)

        actual_target = target_connections or pool_manager.stats.pool_size // 2
        logger.info(f"Database pool warmed up with {actual_target} connections")

        return {
            "message": f"Database pool warmed up with {actual_target} connections",
            "target_connections": actual_target,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error warming up database pool: {e}")
        raise HTTPException(status_code=500, detail="Failed to warm up database pool") from e


# --- 规则注册相关辅助函数 ---


async def _start_registration_task(
    rule_key: str, excel_data: list[dict[str, Any]], template: RuleTemplate, user_id: str
) -> str:
    """
    启动异步规则注册任务

    Args:
        rule_key: 规则键值
        excel_data: Excel数据
        base_rule: 规则模板对象
        user_id: 用户ID

    Returns:
        str: 任务ID

    Raises:
        Exception: 启动任务失败时抛出
    """
    try:
        # 获取任务状态管理器
        task_manager = get_task_status_manager()

        # 创建任务
        task_id = await task_manager.create_task(
            task_type="registration",
            rule_key=rule_key,
            total_operations=len(excel_data),  # 初始估计，后续会更新
            user_id=user_id,
        )

        # 准备注册数据
        registration_data = {"rule_key": rule_key, "excel_data": excel_data, "template": template}

        # 获取注册队列并添加任务
        from master import REGISTRATION_QUEUE

        if REGISTRATION_QUEUE is None:
            raise Exception("注册队列未初始化")

        # 将任务添加到队列
        await REGISTRATION_QUEUE.put((task_id, registration_data))

        logger.info(f"规则注册任务已添加到队列: {task_id}")
        return task_id

    except Exception as e:
        logger.error(f"启动规则注册任务失败: {e}", exc_info=True)
        raise


# --- 任务状态查询接口 ---


@management_router.get("/registration/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_registration_task_status(task_id: str):
    """
    获取注册任务状态

    Args:
        task_id: 任务ID

    Returns:
        TaskStatusResponse: 任务状态信息

    Raises:
        HTTPException: 任务不存在时抛出404错误
    """
    try:
        task_manager = get_task_status_manager()
        task_info = await task_manager.get_task_info(task_id)

        if task_info is None:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        return TaskStatusResponse(**task_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取任务状态失败") from e


@management_router.get("/registration/tasks", response_model=TaskListResponse)
async def list_registration_tasks(
    status_filter: str | None = None,
    rule_key_filter: str | None = None,
    user_id_filter: str | None = None,
    page: int = 1,
    page_size: int = 20,
):
    """
    列出注册任务

    Args:
        status_filter: 状态过滤器
        rule_key_filter: 规则键值过滤器
        user_id_filter: 用户ID过滤器
        page: 页码
        page_size: 页大小

    Returns:
        TaskListResponse: 任务列表
    """
    try:
        task_manager = get_task_status_manager()

        # 转换状态过滤器
        status_enum = None
        if status_filter:
            try:
                status_enum = TaskStatus(status_filter)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的状态值: {status_filter}") from None

        # 获取任务列表
        tasks = await task_manager.list_tasks(
            status_filter=status_enum,
            rule_key_filter=rule_key_filter,
            user_id_filter=user_id_filter,
            limit=page_size * page,  # 简单的分页实现
        )

        # 计算分页
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_tasks = tasks[start_index:end_index]

        # 转换为响应模型
        task_responses = [TaskStatusResponse(**task) for task in paginated_tasks]

        return TaskListResponse(tasks=task_responses, total_count=len(tasks), page=page, page_size=page_size)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="列出任务失败") from e


@management_router.post("/registration/tasks/{task_id}/cancel")
async def cancel_registration_task(task_id: str):
    """
    取消注册任务

    Args:
        task_id: 任务ID

    Returns:
        dict: 取消结果

    Raises:
        HTTPException: 任务不存在或取消失败时抛出错误
    """
    try:
        task_manager = get_task_status_manager()

        # 检查任务是否存在
        task_info = await task_manager.get_task_info(task_id)
        if task_info is None:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        # 检查任务状态
        if task_info["status"] in ["completed", "failed", "cancelled"]:
            raise HTTPException(status_code=400, detail=f"任务 {task_id} 已处于终态 ({task_info['status']})，无法取消")

        # 取消任务
        success = await task_manager.cancel_task(task_id)

        if success:
            return {"message": f"任务 {task_id} 已成功取消"}
        else:
            raise HTTPException(status_code=500, detail=f"取消任务 {task_id} 失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="取消任务失败") from e


# --- 补偿事务和数据一致性相关接口 ---


@management_router.get("/registration/compensation/tasks")
async def list_compensation_tasks():
    """
    获取需要补偿的任务列表

    Returns:
        dict: 补偿任务列表
    """
    try:
        task_manager = get_task_status_manager()

        if not hasattr(task_manager, "get_tasks_needing_compensation"):
            return {"tasks": [], "message": "当前任务管理器不支持补偿功能"}

        compensation_tasks = await task_manager.get_tasks_needing_compensation()

        return {
            "tasks": compensation_tasks,
            "count": len(compensation_tasks),
            "message": f"找到 {len(compensation_tasks)} 个需要补偿的任务",
        }

    except Exception as e:
        logger.error(f"获取补偿任务列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取补偿任务列表失败") from e


@management_router.post("/registration/compensation/process")
async def process_compensation_tasks():
    """
    处理所有需要补偿的任务

    Returns:
        dict: 处理结果
    """
    try:
        from services.task_recovery_service import get_task_recovery_service

        recovery_service = get_task_recovery_service()
        result = await recovery_service.process_compensation_tasks()

        return result

    except Exception as e:
        logger.error(f"处理补偿任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="处理补偿任务失败") from e


@management_router.post("/registration/compensation/tasks/{task_id}/process")
async def process_single_compensation_task(task_id: str, strategy: str = "retry"):
    """
    处理单个补偿任务

    Args:
        task_id: 任务ID
        strategy: 补偿策略 (retry/rollback/manual)

    Returns:
        dict: 处理结果
    """
    try:
        from services.compensation_transaction_service import CompensationStrategy, CompensationTransactionService

        # 验证策略参数
        strategy_map = {
            "retry": CompensationStrategy.RETRY,
            "rollback": CompensationStrategy.ROLLBACK,
            "manual": CompensationStrategy.MANUAL,
        }

        if strategy not in strategy_map:
            raise HTTPException(
                status_code=400, detail=f"无效的补偿策略: {strategy}，支持的策略: {list(strategy_map.keys())}"
            )

        # 获取任务信息
        task_manager = get_task_status_manager()
        task_info = await task_manager.get_task_info(task_id)

        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 执行补偿操作
        compensation_service = CompensationTransactionService()
        await compensation_service.initialize_dependencies()

        result = await compensation_service.process_compensation(task_id, task_info, strategy_map[strategy])

        return {
            "task_id": task_id,
            "strategy": strategy,
            "success": result.success,
            "message": result.message,
            "details": result.details,
            "timestamp": result.timestamp.isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理单个补偿任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="处理补偿任务失败") from e


@management_router.get("/registration/consistency/check", response_model=ApiResponse[dict[str, Any]])
async def check_data_consistency(rule_key: str | None = None):
    """
    检查数据一致性

    Args:
        rule_key: 可选的规则键，如果不提供则检查所有规则

    Returns:
        ApiResponse[dict]: 统一格式的API响应，包含一致性检查结果
    """
    try:
        logger.info(f"开始数据一致性检查: rule_key={rule_key}")

        from services.task_recovery_service import get_task_recovery_service

        recovery_service = get_task_recovery_service()
        result = await recovery_service.run_consistency_check(rule_key)

        return ApiResponse.success_response(data=result, message="数据一致性检查完成")

    except Exception as e:
        logger.error(f"数据一致性检查失败: {e}", exc_info=True)
        return ApiResponse.error_response(code=500, message="数据一致性检查失败，请稍后重试")


@management_router.get("/registration/health", response_model=ApiResponse[dict[str, Any]])
async def registration_health_check():
    """
    规则注册服务健康检查

    Returns:
        ApiResponse[dict]: 统一格式的API响应，包含健康状态信息
    """
    try:
        logger.info("开始规则注册服务健康检查")

        from services.rule_registration_service import RuleRegistrationService
        from services.task_recovery_service import get_task_recovery_service

        # 检查注册服务
        registration_service = RuleRegistrationService()
        registration_health = await registration_service.health_check()

        # 检查任务管理器
        task_manager = get_task_status_manager()
        manager_stats = await task_manager.get_manager_stats()

        # 检查恢复服务
        recovery_service = get_task_recovery_service()

        # 获取补偿任务统计
        compensation_tasks = []
        if hasattr(task_manager, "get_tasks_needing_compensation"):
            compensation_tasks = await task_manager.get_tasks_needing_compensation()

        health_data = {
            "status": "healthy" if registration_health.get("healthy", False) else "unhealthy",
            "registration_service": registration_health,
            "task_manager": manager_stats,
            "compensation_tasks_count": len(compensation_tasks),
            "recovery_service_initialized": recovery_service is not None,
            "timestamp": datetime.now().isoformat(),
        }

        return ApiResponse.success_response(data=health_data, message="健康检查完成")

    except Exception as e:
        logger.error(f"健康检查失败: {e}", exc_info=True)
        error_data = {"status": "unhealthy", "error": str(e), "timestamp": datetime.now().isoformat()}
        return ApiResponse.error_response(code=500, message="健康检查失败", data=error_data)


# ===== 兼容性接口 =====


@management_router.get("/{rule_key}/detail/legacy", response_model=dict[str, Any])
def get_rule_detail_legacy(rule_key: str, session: Session = Depends(get_db_session)):
    """
    获取规则详情（兼容性接口）
    返回传统的 JSON 格式，但数据来源于新的 rule_details 表

    Args:
        rule_key: 规则键
        session: 数据库会话

    Returns:
        dict: 传统格式的规则详情
    """
    try:
        logger.info(f"获取规则详情（兼容模式）: rule_key={rule_key}")

        # 1. 获取规则模板信息
        template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
        if not template:
            raise HTTPException(status_code=404, detail=f"Rule template '{rule_key}' not found.")

        # 2. 获取活跃的规则明细
        rule_details = (
            session.query(RuleDetail)
            .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE))
            .all()
        )

        if not rule_details:
            # 返回空的数据集
            return {
                "rule_key": rule_key,
                "rule_name": template.name,
                "status": template.status.value,
                "version": 0,
                "is_active": False,
                "data_set": [],
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "updated_at": template.updated_at.isoformat() if template.updated_at else None,
            }

        # 3. 转换为传统 JSON 格式（向后兼容）
        legacy_data_set = []
        for detail in rule_details:
            legacy_item = {
                "rule_id": detail.rule_id,
                "rule_name": detail.rule_name,
                # 使用旧字段名保持兼容性
                "error_level_1": detail.level1,
                "error_level_2": detail.level2,
                "error_level_3": detail.level3,
                "error_reason": detail.error_reason,
                "error_severity": detail.degree,
                "quality_basis": detail.reference,
                "location_desc": detail.detail_position,
                "prompt_field_type": detail.prompted_fields3,
                "prompt_field_code": detail.prompted_fields1,
                "rule_category": detail.type,
                "applicable_business": detail.pos,
                "applicable_region": detail.applicableArea,
                "default_selected": detail.default_use == "是",
                "start_date": detail.start_date,
                "end_date": detail.end_date,
                "status": detail.status.value,
                "created_at": detail.created_at.isoformat() if detail.created_at else None,
                "updated_at": detail.updated_at.isoformat() if detail.updated_at else None,
            }

            # 添加扩展字段
            if detail.extended_fields:
                try:
                    import json

                    extended_data = (
                        json.loads(detail.extended_fields)
                        if isinstance(detail.extended_fields, str)
                        else detail.extended_fields
                    )
                    legacy_item.update(extended_data)
                except Exception as e:
                    logger.warning(f"Failed to parse extended fields for rule {detail.rule_id}: {e}")

            # 过滤掉 None 值
            legacy_item = {k: v for k, v in legacy_item.items() if v is not None}
            legacy_data_set.append(legacy_item)

        result = {
            "rule_key": rule_key,
            "rule_name": template.name,
            "status": template.status.value,
            "version": 1,  # 新架构中不再有版本概念
            "is_active": True,
            "data_set": legacy_data_set,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None,
        }

        logger.info(f"获取规则详情（兼容模式）成功: {len(legacy_data_set)} 条明细")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取规则详情（兼容模式）失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get rule detail: {str(e)}") from e

        # 注释：迁移状态查询功能在新架构中不再需要
        # @management_router.get("/{rule_key}/migration-status", response_model=ApiResponse[dict[str, Any]])
        # def get_migration_status(rule_key: str, session: Session = Depends(get_db_session)):


# 注释：迁移状态查询功能在新架构中不再需要，已废弃

# ===== 数据提交重构相关辅助方法 =====

# 注释：在新架构中不再需要数据集概念，此函数已废弃
# def _create_dataset_with_migration_status(
#     session: Session,
#     base_rule_id: int,
#     version: int,
#     user_id: str,
#     data_to_submit: list[dict[str, Any]] = None,
#     enable_json_backup: bool = True,
# ) -> RuleDataSet:


# 注释：在新架构中不再需要数据集概念，相关函数已废弃

# 注释：以下函数在新架构中已废弃，使用RuleDetailService替代
# def _submit_data_to_rule_details(...):
# def _handle_submission_fallback(...):
# 相关实现已移至服务层
# 注释：以上废弃函数的实现已移至服务层
