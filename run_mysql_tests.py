#!/usr/bin/env python3
"""
运行MySQL集成测试的脚本
确保使用真实的MySQL数据库进行测试
"""

import os
import sys
import subprocess
from pathlib import Path


def main():
    """运行MySQL集成测试"""
    print("🚀 开始运行MySQL集成测试...")
    
    # 设置环境变量，强制使用MySQL
    env = os.environ.copy()
    env["USE_MYSQL_FOR_INTEGRATION_TEST"] = "true"
    env["PYTHONPATH"] = str(Path.cwd())
    
    # 运行MySQL专用测试
    mysql_test_file = "tests/integration/test_rule_details_mysql.py"
    
    if not Path(mysql_test_file).exists():
        print(f"❌ 测试文件不存在: {mysql_test_file}")
        return 1
    
    print(f"📋 运行测试文件: {mysql_test_file}")
    print("🔧 使用MySQL数据库: mysql+pymysql://rule_user:***@192.168.100.192:3306/rule_service_test")
    
    # 构建pytest命令
    cmd = [
        sys.executable, "-m", "pytest",
        mysql_test_file,
        "-v",  # 详细输出
        "-s",  # 不捕获输出，显示print语句
        "--tb=short",  # 简短的traceback
        "--confcutdir=tests/integration",  # 指定conftest.py搜索目录
        "-p", "no:warnings",  # 禁用警告
    ]
    
    try:
        # 运行测试
        result = subprocess.run(cmd, env=env, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ MySQL集成测试全部通过！")
        else:
            print("❌ MySQL集成测试失败！")
            
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
