from config.settings import settings
from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class DrugLimitMaxPayDaysRule(BaseRule):
    """
    药品限最大支付天数-规则
    """

    rule_key = "drug_limit_max_pay_days"
    rule_name = "药品限最大支付天数"
    rule_desc = """1、排除自费患者、自费费用
2、匹配目标药品医保代码
3、违规时间为收费时间最后几天"""

    def __init__(
        self,
        rule_id: str,                    # 规则ID
        yb_code: list[str],              # 药品编码
        limit_days: int,                 # 限制天数
        rule_name: str,                  # 规则名称
        level1: str,                     # 一级错误类型
        level2: str,                     # 二级错误类型
        level3: str,                     # 三级错误类型
        error_reason: str,               # 错误原因
        degree: str,                     # 错误程度
        reference: str,                  # 质控依据或参考资料
        detail_position: str,            # 具体位置描述
        prompted_fields3: str | None,    # 提示字段类型
        prompted_fields1: str,           # 提示字段编码
        type: str,                       # 规则类别
        pos: str,                        # 适用业务
        applicableArea: str,             # 适用地区
        default_use: str,                # 默认选用
        remarks: str | None,             # 备注信息
        in_illustration: str | None,     # 入参说明
        start_date: str,                 # 开始日期
        end_date: str,                   # 结束日期
    ):
        super().__init__(rule_id)
        self.yb_codes = yb_code
        self.limit_days = limit_days
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        药品限最大支付天数 规则的校验逻辑
        """
        # 先判断病人医保类型，如果 为空、非字符串、自费就返回None
        if (
            not patient_data.patientMedicalInsuranceType
            or not isinstance(patient_data.patientMedicalInsuranceType, str)
            or "自费" in patient_data.patientMedicalInsuranceType
        ):
            return None

        # 疑似违规数据，格式： 违规日期：(费用id列表，当日违规总数量，当日违规金额)
        illegal_data = {}
        # 使用天数、
        used_dates = set()
        # 使用数量
        used_count = 0

        for fee in patient_data.fees:
            # 判断药品编码是否在违规编码列表中
            if fee.ybdm not in self.yb_codes:
                continue

            # 判断日期是否正确，必须是毫秒级时间戳
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue

            fee_date = self._trans_timestamp_to_date(int(jzsj_str[:10]))
            # 下面会把自费的药品剔除掉，但是总使用量和总使用天数需要统计
            # 所以要在剔除前统计
            used_dates.add(fee_date)
            used_count += fee.sl

            # 判断是否医保结算
            if str(fee.bzjs) in settings.FEE_SELF_PAY_CODE:  # "400"、"300"、"0"、"null" 表示自费
                continue

            # 整合待定违规数据
            if fee_date not in illegal_data:
                illegal_data[fee_date] = [[fee.id], fee.sl, fee.je]
            else:
                illegal_data[fee_date][0].append(fee.id)
                illegal_data[fee_date][1] += fee.sl
                illegal_data[fee_date][2] += fee.je

        if not illegal_data:
            return None

        # 违规的日期集合
        illegal_dates = set()
        # 违规项目列表，即费用id列表
        illegal_fee_ids = []
        # 违规数据
        illegal_day, illegal_count, error_fee = 0, 0, 0
        # 所有项目费用列表
        all_fee_ids = []

        # 将疑似违规数据按违规日期排序
        for fee_date in sorted(illegal_data.keys()):
            # 先把当前日期加入违规日期集合
            illegal_dates.add(fee_date)

            # 违规数据
            fee_id_list, sl, je = illegal_data[fee_date]

            # 将当前日期所有项目费用id加入所有项目费用列表
            all_fee_ids.extend(fee_id_list)

            # 判断违规日期集合是否超过限制天数
            if len(illegal_dates) > self.limit_days:
                illegal_day += 1  # 违规天数
                illegal_count += sl  # 违规数量
                error_fee += je  # 涉及金额
                illegal_fee_ids.extend(fee_id_list)  # 违规项目列表

        # 根据违规天数来判断是否违规，如果违规天数还是0,那就说明没有违规
        if illegal_day == 0:
            return None

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            used_count=used_count,
            illegal_count=illegal_count,
            used_day=len(used_dates),
            illegal_day=illegal_day,
            illegal_item=",".join(illegal_fee_ids),
            error_fee=error_fee,
            prompted_fields2=",".join(all_fee_ids),
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
