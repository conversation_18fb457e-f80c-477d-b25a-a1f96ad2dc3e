#!/usr/bin/env python3
"""
测试 yb_code 字段数据校验修复 - 单元测试版本
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pydantic import ValidationError
from models.api import CreateRuleDetailRequest, UpdateRuleDetailRequest


def test_create_request_with_list():
    """测试CreateRuleDetailRequest接受列表格式的数组字段"""
    print("测试1: CreateRuleDetailRequest 接受列表格式")

    try:
        # 测试数据 - 使用列表格式
        request_data = {
            "rule_id": "TEST_001",
            "rule_name": "测试规则",
            "level1": "用药安全",
            "level2": "适应症检查",
            "level3": "药品限制",
            "error_reason": "药品与诊断不匹配",
            "degree": "严重",
            "reference": "药品说明书",
            "detail_position": "处方明细",
            "prompted_fields1": "药品编码",
            "type": "限制性规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["A01AA01", "A01AA02", "A01AA03"],  # 列表格式
            "diag_whole_code": ["K02.1", "K02.2"],
            "diag_code_prefix": ["K02"],
            "fee_whole_code": ["C001", "C002"],
            "fee_code_prefix": ["C00"],
        }

        request = CreateRuleDetailRequest(**request_data)
        print(f"✅ 测试1通过: 成功创建请求对象")
        print(f"   yb_code: {request.yb_code}")
        print(f"   diag_whole_code: {request.diag_whole_code}")
        return True

    except ValidationError as e:
        print(f"❌ 测试1失败: Pydantic验证错误")
        print(f"   错误详情: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试1异常: {str(e)}")
        return False


def test_create_request_with_string():
    """测试CreateRuleDetailRequest拒绝字符串格式的数组字段"""
    print("\n测试2: CreateRuleDetailRequest 拒绝字符串格式")

    try:
        # 测试数据 - 使用字符串格式
        request_data = {
            "rule_id": "TEST_002",
            "rule_name": "测试规则",
            "level1": "用药安全",
            "level2": "适应症检查",
            "level3": "药品限制",
            "error_reason": "药品与诊断不匹配",
            "degree": "严重",
            "reference": "药品说明书",
            "detail_position": "处方明细",
            "prompted_fields1": "药品编码",
            "type": "限制性规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": "A01AA01,A01AA02,A01AA03",  # 字符串格式
            "diag_whole_code": "K02.1,K02.2",
            "diag_code_prefix": "K02",
            "fee_whole_code": "C001,C002",
            "fee_code_prefix": "C00",
        }

        request = CreateRuleDetailRequest(**request_data)
        print(f"❌ 测试2失败: 不应该接受字符串格式")
        print(f"   yb_code: {request.yb_code} (类型: {type(request.yb_code)})")
        return False

    except ValidationError as e:
        print(f"✅ 测试2通过: 正确拒绝字符串格式")
        print(f"   验证错误: {e}")
        return True
    except Exception as e:
        print(f"❌ 测试2异常: {str(e)}")
        return False


def test_update_request_with_list():
    """测试UpdateRuleDetailRequest接受列表格式的数组字段"""
    print("\n测试3: UpdateRuleDetailRequest 接受列表格式")

    try:
        # 测试数据 - 使用列表格式
        request_data = {
            "rule_name": "更新测试规则",
            "yb_code": ["A01AA01", "A01AA02"],  # 列表格式
            "diag_whole_code": ["K02.1"],
        }

        request = UpdateRuleDetailRequest(**request_data)
        print(f"✅ 测试3通过: 成功创建更新请求对象")
        print(f"   yb_code: {request.yb_code}")
        return True

    except ValidationError as e:
        print(f"❌ 测试3失败: Pydantic验证错误")
        print(f"   错误详情: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试3异常: {str(e)}")
        return False


def test_empty_and_none_values():
    """测试空值和None值的处理"""
    print("\n测试4: 空值和None值处理")

    try:
        # 测试数据 - 包含空值和None
        request_data = {
            "rule_id": "TEST_004",
            "rule_name": "空值测试规则",
            "level1": "用药安全",
            "level2": "适应症检查",
            "level3": "药品限制",
            "error_reason": "测试",
            "degree": "严重",
            "reference": "测试",
            "detail_position": "测试",
            "prompted_fields1": "测试",
            "type": "测试",
            "pos": "测试",
            "applicableArea": "测试",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": None,  # None值
            "diag_whole_code": [],  # 空列表
            "diag_code_prefix": ["K02"],  # 正常列表
        }

        request = CreateRuleDetailRequest(**request_data)
        print(f"✅ 测试4通过: 正确处理空值和None值")
        print(f"   yb_code: {request.yb_code}")
        print(f"   diag_whole_code: {request.diag_whole_code}")
        print(f"   diag_code_prefix: {request.diag_code_prefix}")
        return True

    except ValidationError as e:
        print(f"❌ 测试4失败: Pydantic验证错误")
        print(f"   错误详情: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试4异常: {str(e)}")
        return False


def test_array_field_conversion():
    """测试数组字段转换逻辑"""
    print("\n测试5: 数组字段转换逻辑")

    try:
        from services.unified_data_mapping_engine import UnifiedDataMappingEngine

        engine = UnifiedDataMappingEngine()

        # 测试数据 - 包含列表格式的数组字段
        test_data = {
            "rule_id": "TEST_005",
            "rule_name": "数组转换测试",
            "yb_code": ["A01AA01", "A01AA02", "A01AA03"],
            "diag_whole_code": ["K02.1", "K02.2"],
            "diag_code_prefix": ["K02"],
            "fee_whole_code": ["C001", "C002"],
            "fee_code_prefix": ["C00"],
        }

        # 标准化字段名
        normalized_data = engine.normalize_field_names(test_data)

        # 分离固定字段和扩展字段（这里会进行数组字段转换）
        fixed_fields, extended_fields = engine.separate_fields(normalized_data)

        print(f"✅ 测试5通过: 数组字段转换成功")
        print(f"   原始 yb_code: {test_data['yb_code']} (类型: {type(test_data['yb_code'])})")
        print(f"   转换后 yb_code: {fixed_fields.get('yb_code')} (类型: {type(fixed_fields.get('yb_code'))})")
        print(f"   转换后 diag_whole_code: {fixed_fields.get('diag_whole_code')}")

        # 验证转换结果
        expected_yb_code = "A01AA01,A01AA02,A01AA03"
        actual_yb_code = fixed_fields.get("yb_code")

        if actual_yb_code == expected_yb_code:
            print(f"   ✅ yb_code 转换正确: {actual_yb_code}")
            return True
        else:
            print(f"   ❌ yb_code 转换错误: 期望 '{expected_yb_code}', 实际 '{actual_yb_code}'")
            return False

    except Exception as e:
        print(f"❌ 测试5异常: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("开始测试 yb_code 字段数据校验修复...")
    print("=" * 60)

    # 运行测试
    test1_passed = test_create_request_with_list()
    test2_passed = test_create_request_with_string()
    test3_passed = test_update_request_with_list()
    test4_passed = test_empty_and_none_values()
    test5_passed = test_array_field_conversion()

    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"测试1 (创建请求-列表格式): {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"测试2 (创建请求-字符串格式): {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"测试3 (更新请求-列表格式): {'✅ 通过' if test3_passed else '❌ 失败'}")
    print(f"测试4 (空值处理): {'✅ 通过' if test4_passed else '❌ 失败'}")
    print(f"测试5 (数组字段转换): {'✅ 通过' if test5_passed else '❌ 失败'}")

    passed_count = sum([test1_passed, test2_passed, test3_passed, test4_passed, test5_passed])
    total_count = 5

    if passed_count == total_count:
        print(f"\n🎉 所有测试通过！({passed_count}/{total_count}) yb_code 字段数据校验修复成功")
        sys.exit(0)
    else:
        print(f"\n❌ 部分测试失败 ({passed_count}/{total_count})，需要进一步检查")
        sys.exit(1)


if __name__ == "__main__":
    main()
