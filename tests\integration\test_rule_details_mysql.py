"""
规则详情表API MySQL集成测试
使用真实MySQL数据库进行测试，确保与生产环境一致
"""

import json
import pytest
from sqlalchemy.orm import Session

from models.database import RuleDetail, RuleTemplate, RuleTemplateStatusEnum


class TestRuleDetailsAPIMysql:
    """规则明细API MySQL集成测试"""

    @pytest.fixture(autouse=True)
    def setup(self, mysql_db_session: Session, mysql_client):
        """测试前置设置"""
        self.client = mysql_client
        self.session = mysql_db_session
        self.headers = {"X-API-KEY": "a_very_secret_key_for_development"}

        # 创建测试规则模板
        self.test_template = RuleTemplate(
            rule_key="test_rule_mysql",
            rule_type="MySQL测试规则",
            name="MySQL测试规则",
            description="用于MySQL API测试的规则模板",
            status=RuleTemplateStatusEnum.NEW,
        )
        self.session.add(self.test_template)
        self.session.commit()
        # 刷新会话以确保数据在其他连接中可见
        self.session.flush()

    def test_create_rule_detail_with_mysql(self):
        """测试在MySQL中创建规则明细"""
        rule_data = {
            "rule_id": "MYSQL_TEST_001",
            "rule_key": "test_rule_mysql",
            "rule_name": "MySQL测试规则明细",
            "level1": "用药安全",
            "level2": "适应症限制",
            "level3": "年龄限制",
            "error_reason": "该药品仅适用于成人患者",
            "degree": "严重",
            "reference": "药品说明书第3条",
            "detail_position": "处方明细",
            "prompted_fields1": "age",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "remarks": "MySQL测试备注",
            "in_illustration": "MySQL测试说明",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["MYSQL_YB_001"],
            "diag_whole_code": ["MYSQL_DIAG_001"],
            "diag_code_prefix": ["MYSQL_PREFIX"],
            "diag_name_keyword": "MySQL测试关键词",
            "fee_whole_code": ["MYSQL_FEE_001"],
            "fee_code_prefix": ["MYSQL_FEE_PREFIX"],
        }

        response = self.client.post("/api/v1/rules/details/test_rule_mysql", json=rule_data, headers=self.headers)

        assert response.status_code == 200
        result = response.json()
        print("MySQL测试结果:")
        print("*" * 100)
        print(result)
        print("*" * 100)
        assert result["success"] is True

        # 验证数据库中的数据
        detail = self.session.query(RuleDetail).filter(RuleDetail.rule_id == "MYSQL_TEST_001").first()
        assert detail is not None
        assert detail.id is not None  # MySQL自增ID应该正常生成
        assert detail.rule_key == "test_rule_mysql"
        assert detail.rule_name == "MySQL测试规则明细"
        assert detail.level1 == "用药安全"
        assert detail.level2 == "适应症限制"
        assert detail.level3 == "年龄限制"
        assert detail.degree == "严重"
        assert detail.reference == "药品说明书第3条"
        assert detail.yb_code == "MYSQL_YB_001"
        assert detail.diag_whole_code == "MYSQL_DIAG_001"

    def test_field_mapping_consistency_mysql(self):
        """测试MySQL环境下的字段映射一致性"""
        rule_data = {
            "rule_id": "MYSQL_MAPPING_TEST",
            "rule_key": "test_rule_mysql",
            "rule_name": "MySQL字段映射测试",
            "level1": "用药安全",
            "level2": "适应症限制",
            "level3": "年龄限制",
            "error_reason": "测试错误原因",
            "degree": "严重",
            "reference": "测试质控依据",
            "detail_position": "测试位置",
            "prompted_fields3": "test_type",
            "prompted_fields1": "test_code",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "remarks": "测试备注",
            "in_illustration": "测试说明",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["TEST_YB_001"],
            "diag_whole_code": ["TEST_DIAG_001"],
            "diag_code_prefix": ["TEST_PREFIX"],
            "diag_name_keyword": "测试关键词",
            "fee_whole_code": ["TEST_FEE_001"],
            "fee_code_prefix": ["FEE_PREFIX"],
        }

        response = self.client.post("/api/v1/rules/details/test_rule_mysql", json=rule_data, headers=self.headers)

        assert response.status_code == 200
        result = response.json()
        print("MySQL字段映射测试结果:")
        print("*" * 100)
        print(result)
        print("*" * 100)
        assert result["success"] is True

        # 验证数据库中的字段映射
        detail = self.session.query(RuleDetail).filter(RuleDetail.rule_id == "MYSQL_MAPPING_TEST").first()
        assert detail is not None
        assert detail.id is not None  # 确认MySQL自增ID正常工作
        assert detail.level1 == "用药安全"
        assert detail.level2 == "适应症限制"
        assert detail.level3 == "年龄限制"
        assert detail.degree == "严重"
        assert detail.reference == "测试质控依据"
        assert detail.yb_code == "TEST_YB_001"
        assert detail.diag_whole_code == "TEST_DIAG_001"

    def test_mysql_autoincrement_behavior(self):
        """专门测试MySQL自增字段行为"""
        # 创建多个规则明细，验证ID自增
        rule_data_base = {
            "rule_key": "test_rule_mysql",
            "rule_name": "自增测试",
            "level1": "测试",
            "level2": "测试",
            "level3": "测试",
            "error_reason": "测试",
            "degree": "轻微",
            "reference": "测试",
            "detail_position": "测试",
            "type": "测试规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
        }

        created_ids = []
        for i in range(3):
            rule_data = rule_data_base.copy()
            rule_data["rule_id"] = f"AUTO_INCREMENT_TEST_{i}"
            
            response = self.client.post("/api/v1/rules/details/test_rule_mysql", json=rule_data, headers=self.headers)
            assert response.status_code == 200
            result = response.json()
            assert result["success"] is True
            
            # 获取创建的记录ID
            detail = self.session.query(RuleDetail).filter(RuleDetail.rule_id == f"AUTO_INCREMENT_TEST_{i}").first()
            assert detail is not None
            assert detail.id is not None
            created_ids.append(detail.id)

        # 验证ID是递增的
        assert len(set(created_ids)) == 3  # 所有ID都不同
        assert created_ids == sorted(created_ids)  # ID是递增的
        print(f"MySQL自增ID测试通过，生成的ID: {created_ids}")
