"""
MySQL集成测试配置
专门用于需要真实MySQL数据库的集成测试
"""

import os
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi import FastAPI
from fastapi.testclient import TestClient

from models.database import Base


@pytest.fixture(scope="session")
def mysql_test_engine():
    """MySQL测试数据库引擎"""
    # 使用专门的测试数据库
    database_url = "mysql+pymysql://rule_user:mysql_password@192.168.100.192:3306/rule_service_test"
    
    engine = create_engine(
        database_url,
        pool_size=5,
        max_overflow=10,
        pool_timeout=30,
        pool_recycle=3600,
        pool_pre_ping=True,
        echo=False,  # 设置为True可以看到SQL语句
    )
    
    # 删除并重新创建所有表
    Base.metadata.drop_all(engine)
    Base.metadata.create_all(engine)
    
    yield engine
    
    # 清理测试数据
    Base.metadata.drop_all(engine)
    engine.dispose()


@pytest.fixture
def mysql_db_session(mysql_test_engine):
    """MySQL数据库会话"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=mysql_test_engine)
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def mysql_client(mysql_test_engine):
    """使用MySQL的FastAPI测试客户端"""
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["RUN_MODE"] = "TEST"
    os.environ["MASTER_API_SECRET_KEY"] = "a_very_secret_key_for_development"
    
    # 创建简化的测试应用
    app = FastAPI(title="MySQL Test App", version="1.0.0")
    
    # 只包含规则详情相关的路由
    from api.routers.master.rule_details import rule_details_router
    app.include_router(rule_details_router)
    
    # 覆盖数据库依赖为MySQL测试数据库
    from core.db_session import get_db_session
    
    def override_get_db_session():
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=mysql_test_engine)
        session = TestingSessionLocal()
        try:
            yield session
        finally:
            session.close()
    
    app.dependency_overrides[get_db_session] = override_get_db_session
    
    # 添加统一错误处理
    try:
        from api.middleware.error_handling import UnifiedErrorHandlingMiddleware
        UnifiedErrorHandlingMiddleware.register_handlers(app)
    except ImportError:
        # 如果错误处理中间件不存在，跳过
        pass
    
    with TestClient(app) as test_client:
        yield test_client
    
    # 清理环境变量
    os.environ.pop("TESTING", None)
    os.environ.pop("RUN_MODE", None)
    os.environ.pop("MASTER_API_SECRET_KEY", None)
